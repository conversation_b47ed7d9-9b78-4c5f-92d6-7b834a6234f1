html,
body {
  height: 100%;
  margin: 0;
}

.aicall-root {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 9;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #fff;
  max-width: 576px;

  --adm-color-primary: #624aff;
  --adm-mask-z-index: 8;
  .adm-popup {
    position: static;
  }
  .adm-mask {
    position: absolute;
  }
  .adm-center-popup {
    position: static;
    .adm-center-popup-mask {
      z-index: 8;
    }
  }

  .adm-center-popup-wrap,
  .adm-popup-body {
    position: absolute;
    z-index: 8;
  }

  .adm-toast-mask {
    z-index: 9;
    .adm-toast-wrap {
      position: absolute;
    }
  }
}
.layout-header {
  display: none;
}

@media screen and (min-width: 768px) {
  #root {
    max-width: initial;
    padding-top: 56px;
  }

  .layout-header {
    display: flex;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 6;
    height: 55px;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    padding: 0 24px;
    border-bottom: 1px solid rgba(38, 36, 76, 0.05);
    justify-content: flex-start;
    align-items: center;

    ._ph {
      flex: 1;
    }

    ._logout-btn {
      border: none;
    }
  }
}
