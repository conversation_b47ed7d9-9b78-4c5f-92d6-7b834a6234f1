package cn.zhentao.service;

import cn.zhentao.entity.QuestionRecord;
import cn.zhentao.mapper.QuestionRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目记录服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuestionRecordService extends ServiceImpl<QuestionRecordMapper, QuestionRecord> {
    
    /**
     * 保存题目记录
     */
    public QuestionRecord saveQuestionRecord(String questionText, String questionType, 
                                           String subject, String knowledgePoints,
                                           Integer difficultyLevel, String analysis,
                                           String solutionSteps, String finalAnswer,
                                           Double confidenceScore, String imagePath) {
        try {
            QuestionRecord record = new QuestionRecord();
            record.setQuestionText(questionText);
            record.setQuestionType(questionType);
            record.setSubject(subject);
            record.setKnowledgePoints(knowledgePoints);
            record.setDifficultyLevel(difficultyLevel);
            record.setAnalysis(analysis);
            record.setSolutionSteps(solutionSteps);
            record.setFinalAnswer(finalAnswer);
            record.setConfidenceScore(confidenceScore);
            record.setImagePath(imagePath);
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            
            this.save(record);
            log.info("题目记录保存成功，ID: {}", record.getId());
            return record;
        } catch (Exception e) {
            log.error("保存题目记录失败", e);
            throw new RuntimeException("保存题目记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据题目内容查找相似题目
     */
    public List<QuestionRecord> findSimilarQuestions(String questionText, int limit) {
        try {
            QueryWrapper<QuestionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("question_text", questionText)
                       .orderByDesc("confidence_score")
                       .last("LIMIT " + limit);
            
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("查找相似题目失败", e);
            return List.of();
        }
    }
    
    /**
     * 根据学科和题型查找题目
     */
    public List<QuestionRecord> findBySubjectAndType(String subject, String questionType, int limit) {
        try {
            QueryWrapper<QuestionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("subject", subject)
                       .eq("question_type", questionType)
                       .orderByDesc("create_time")
                       .last("LIMIT " + limit);
            
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("根据学科和题型查找题目失败", e);
            return List.of();
        }
    }
    
    /**
     * 获取最近的题目记录
     */
    public List<QuestionRecord> getRecentQuestions(int limit) {
        try {
            QueryWrapper<QuestionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("create_time")
                       .last("LIMIT " + limit);
            
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取最近题目记录失败", e);
            return List.of();
        }
    }
}
