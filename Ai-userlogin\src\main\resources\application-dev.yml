# 开发环境配置
spring:
  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # 禁用Redis（开发环境）
  data:
    redis:
      repositories:
        enabled: false
  redis:
    host: localhost
    port: 6379
    timeout: 3000ms
    # 开发环境禁用Redis连接
    lettuce:
      pool:
        enabled: false

# 开发环境数据库配置
datasource:
  url: jdbc:mysql://**************:3306/ai_tutor?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
  username: root
  password: Sunshuo0818
  driver-class-name: com.mysql.cj.jdbc.Driver

# 开发环境日志配置
logging:
  level:
    cn.zhentao: debug
    org.springframework.cloud: debug
    com.alibaba.nacos: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
