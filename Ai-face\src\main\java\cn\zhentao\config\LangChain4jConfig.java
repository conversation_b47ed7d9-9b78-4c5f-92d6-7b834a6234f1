package cn.zhentao.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * AI服务配置类
 * 使用RestTemplate直接调用通义千问API
 *
 * <AUTHOR>
 */
@Configuration
public class LangChain4jConfig {

    @Value("${ai.api-key:sk-8a4bac4735da4f79b19383d2b2696eac}")
    private String apiKey;

    @Value("${ai.base-url:https://dashscope.aliyuncs.com/compatible-mode/v1}")
    private String baseUrl;

    @Value("${ai.model-name:qwen-vl-plus}")
    private String modelName;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public String getModelName() {
        return modelName;
    }
}
