package cn.zhentao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 题目记录实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("question_record")
public class QuestionRecord {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 题目内容（OCR识别的文字）
     */
    private String questionText;
    
    /**
     * 题目类型（选择题、填空题、解答题等）
     */
    private String questionType;
    
    /**
     * 学科
     */
    private String subject;
    
    /**
     * 知识点
     */
    private String knowledgePoints;
    
    /**
     * 难度等级（1-5）
     */
    private Integer difficultyLevel;
    
    /**
     * AI分析结果
     */
    private String analysis;
    
    /**
     * 解题步骤
     */
    private String solutionSteps;
    
    /**
     * 最终答案
     */
    private String finalAnswer;
    
    /**
     * 置信度分数
     */
    private Double confidenceScore;
    
    /**
     * 图片路径（如果有上传图片）
     */
    private String imagePath;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
