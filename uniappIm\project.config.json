{"description": "项目配置文件", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "minified": true, "checkSiteMap": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "touristappid", "projectname": "uniappIm", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}