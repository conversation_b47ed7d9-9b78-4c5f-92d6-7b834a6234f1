package com.zhentao.studyim.service;

import com.zhentao.studyim.dto.MessageDto;
import com.zhentao.studyim.entity.Message;
import com.zhentao.studyim.entity.User;
import com.zhentao.studyim.repository.MessageRepository;
import com.zhentao.studyim.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息业务逻辑服务类
 */
@Service
@RequiredArgsConstructor
public class MessageService {

    private final MessageRepository messageRepository;
    private final UserRepository userRepository;

    /**
     * 保存消息到数据库
     * @param message 消息对象
     * @return 保存后的消息
     */
    public Message saveMessage(Message message) {
        return messageRepository.save(message);
    }

    /**
     * 发送消息
     * @param fromUserId 发送者ID
     * @param toUserId 接收者ID
     * @param content 消息内容
     * @return 消息DTO
     */
    public MessageDto sendMessage(Long fromUserId, Long toUserId, String content) {
        // 1. 创建消息实体
        Message message = new Message();
        message.setFromUserId(fromUserId);
        message.setToUserId(toUserId);
        message.setContent(content);
        message.setType(Message.MessageType.TEXT);
        message.setStatus(Message.MessageStatus.SENT);

        // 2. 保存到数据库
        Message savedMessage = messageRepository.save(message);

        // 3. 转换为DTO并返回
        return convertToDto(savedMessage);
    }

    /**
     * 获取两个用户之间的聊天历史
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 消息DTO列表
     */
    public List<MessageDto> getChatHistory(Long userId1, Long userId2) {
        // 1. 从数据库查询消息
        List<Message> messages = messageRepository.findChatHistory(userId1, userId2);

        // 2. 转换为DTO对象
        return messages.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    /**
     * 将Message实体转换为MessageDto
     * @param message 消息实体
     * @return 消息DTO
     */
    private MessageDto convertToDto(Message message) {
        MessageDto dto = new MessageDto();
        dto.setId(message.getId());
        dto.setFromUserId(message.getFromUserId());
        dto.setToUserId(message.getToUserId());
        dto.setContent(message.getContent());
        dto.setType(message.getType().name());
        dto.setSendTime(message.getSendTime());

        // 查询用户名
        User fromUser = userRepository.findById(message.getFromUserId()).orElse(null);
        User toUser = userRepository.findById(message.getToUserId()).orElse(null);

        if (fromUser != null) {
            dto.setFromUsername(fromUser.getNickname());
        }
        if (toUser != null) {
            dto.setToUsername(toUser.getNickname());
        }

        return dto;
    }
}