package cn.zhentao.service;

import cn.zhentao.mapper.SearchRecordMapper;
import cn.zhentao.pojo.SearchRecord;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 搜题记录服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class SearchRecordService extends ServiceImpl<SearchRecordMapper, SearchRecord> {
    
    /**
     * 保存搜题记录
     */
    public boolean saveSearchRecord(SearchRecord record) {
        try {
            boolean result = this.save(record);
            if (result) {
                log.info("💾 搜题记录保存成功 - ID: {}, 用户: {}, 学科: {}", 
                    record.getId(), record.getUserId(), record.getSubject());
            }
            return result;
        } catch (Exception e) {
            log.error("💾 搜题记录保存失败", e);
            return false;
        }
    }
    
    /**
     * 根据图片哈希查询缓存记录
     */
    public SearchRecord getByImageHash(String imageHash) {
        try {
            return this.baseMapper.selectByImageHash(imageHash);
        } catch (Exception e) {
            log.error("查询缓存记录失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 查询错题本
     */
    public List<SearchRecord> getWrongQuestions(Long userId, String subject, 
                                              Integer masteryStatus, Integer page, Integer size) {
        try {
            int offset = (page - 1) * size;
            return this.baseMapper.selectWrongQuestions(userId, true, subject, masteryStatus, offset, size);
        } catch (Exception e) {
            log.error("查询错题本失败", e);
            return List.of();
        }
    }
    
    /**
     * 统计错题数量
     */
    public Long countWrongQuestions(Long userId, String subject, Integer masteryStatus) {
        try {
            return this.baseMapper.countWrongQuestions(userId, true, subject, masteryStatus);
        } catch (Exception e) {
            log.error("统计错题数量失败", e);
            return 0L;
        }
    }
    
    /**
     * 更新错题状态
     */
    public boolean updateWrongStatus(Long recordId, Long userId, Boolean isWrong) {
        try {
            int result = this.baseMapper.updateWrongStatus(recordId, userId, isWrong);
            return result > 0;
        } catch (Exception e) {
            log.error("更新错题状态失败", e);
            return false;
        }
    }
    
    /**
     * 更新掌握状态
     */
    public boolean updateMasteryStatus(Long recordId, Integer masteryStatus) {
        try {
            int result = this.baseMapper.updateMasteryStatus(recordId, masteryStatus);
            return result > 0;
        } catch (Exception e) {
            log.error("更新掌握状态失败", e);
            return false;
        }
    }
    
    /**
     * 查询相似题目
     */
    public List<SearchRecord> getSimilarQuestions(String knowledgePoint, String subject,
                                                String questionType, Long excludeId, Integer limit) {
        try {
            return this.baseMapper.selectSimilarQuestions(knowledgePoint, subject, questionType, excludeId, limit);
        } catch (Exception e) {
            log.error("查询相似题目失败", e);
            return List.of();
        }
    }

    /**
     * 查询会话历史
     */
    public List<SearchRecord> getSessionHistory(Long userId, String sessionName, Integer page, Integer size) {
        try {
            return this.baseMapper.selectSessionHistory(userId, sessionName, (page - 1) * size, size);
        } catch (Exception e) {
            log.error("查询会话历史失败", e);
            return List.of();
        }
    }

    /**
     * 统计会话历史数量
     */
    public Long countSessionHistory(Long userId, String sessionName) {
        try {
            return this.baseMapper.countSessionHistory(userId, sessionName);
        } catch (Exception e) {
            log.error("统计会话历史数量失败", e);
            return 0L;
        }
    }
}
