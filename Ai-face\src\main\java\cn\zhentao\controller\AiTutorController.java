package cn.zhentao.controller;

import cn.zhentao.entity.QuestionRecord;
import cn.zhentao.mapper.*;
import cn.zhentao.pojo.TutorMessage;
import cn.zhentao.pojo.TutorQuestion;
import cn.zhentao.pojo.TutorSession;
import cn.zhentao.service.AiChatService;
import cn.zhentao.service.MinioFileService;
import cn.zhentao.service.SearchRecordService;
import cn.zhentao.utils.AliyunOcrUtil;
import cn.zhentao.utils.ImageHashUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI智能搜题系统控制器
 * 集成拍照搜题、错题管理、会话管理、学习统计等功能
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-tutor")
@Api(tags = "AI智能搜题系统")
@Slf4j
public class AiTutorController {

    @Autowired
    private AiChatService aiChatService;

    @Autowired
    private SearchRecordService searchRecordService;

    @Autowired
    private MinioFileService minioFileService;

    @Autowired
    private AliyunOcrUtil aliyunOcrUtil;

    @Autowired
    private QuestionRecordMapper questionRecordMapper;

    @Autowired
    private SearchRecordMapper searchRecordMapper;

    @Autowired
    private TutorQuestionMapper tutorQuestionMapper;

    @Autowired
    private TutorSessionMapper tutorSessionMapper;

    @Autowired
    private TutorMessageMapper tutorMessageMapper;

    @Autowired
    private TutorLearningStatsMapper tutorLearningStatsMapper;

    /**
     * 拍照搜题一体化功能 - 核心功能
     * 集成OCR识别、AI分析、相似题目生成、对话功能的完整解决方案
     *
     * @param userId 用户ID
     * @param sessionName 会话名称（可选）
     * @param imageFile 题目图片文件
     * @return 完整的搜题结果，包含解答、相似题目和对话会话
     */
    @PostMapping("/integrated-search")
    @ApiOperation(value = "拍照搜题一体化", notes = "上传题目图片，AI智能分析并生成详细解答、相似题目，支持后续对话")
    public Map<String, Object> integratedQuestionSearch(
            @ApiParam(value = "用户ID", required = true, example = "1") @RequestParam Long userId,
            @ApiParam(value = "会话名称", required = false, example = "数学练习") @RequestParam(required = false) String sessionName,
            @ApiParam(value = "题目图片文件", required = true) @RequestParam("imageFile") MultipartFile imageFile) {

        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            log.info("🔍 收到一体化搜题请求 - 用户ID: {}, 会话: {}, 文件: {}, 大小: {}KB",
                userId, sessionName, imageFile.getOriginalFilename(), imageFile.getSize() / 1024);

            // 1. 参数验证
            if (userId == null || imageFile == null || imageFile.isEmpty()) {
                result.put("success", false);
                result.put("message", "参数错误：用户ID和图片文件不能为空");
                return result;
            }

            // 2. 计算图片哈希值，检查是否已处理过
            String imageHash = ImageHashUtil.calculateHash(imageFile.getBytes());
            log.info("📊 图片哈希值: {}", imageHash);

            // 检查缓存
            TutorQuestion cachedQuestion = tutorQuestionMapper.getQuestionByImageHash(imageHash);
            if (cachedQuestion != null) {
                log.info("💾 发现缓存题目，直接返回结果");
                return buildCachedResult(cachedQuestion, userId, sessionName);
            }

            // 3. OCR识别文字和公式
            log.info("🔤 开始OCR识别...");
            String ocrResult = aliyunOcrUtil.recognizeTextByStream(
                imageFile.getInputStream(),
                imageFile.getOriginalFilename()
            );

            JSONObject ocrJson = JSON.parseObject(ocrResult);
            String ocrText = extractTextFromOcrResult(ocrJson);
            String ocrLatex = extractLatexFromOcrResult(ocrJson);

            log.info("📝 OCR识别完成 - 文字长度: {}, LaTeX长度: {}", ocrText.length(),
                ocrLatex != null ? ocrLatex.length() : 0);

            // 4. 上传图片到MinIO
            log.info("📁 开始上传图片到MinIO...");
            String imageUrl = null;
            try {
                imageUrl = minioFileService.uploadQuestionImage(imageFile);
            } catch (Exception e) {
                log.warn("📁 图片上传失败，继续处理: {}", e.getMessage());
            }

            // 5. AI智能分析题目
            log.info("🤖 开始AI智能分析...");
            Map<String, Object> aiAnalysisResult = performComprehensiveAnalysis(ocrText, ocrLatex);

            // 6. 创建或获取会话
            TutorSession session = createOrGetSession(userId, sessionName);

            // 7. 保存题目记录到数据库
            TutorQuestion question = createTutorQuestion(userId, session.getId(), imageUrl, imageHash,
                ocrText, ocrLatex, aiAnalysisResult);
            tutorQuestionMapper.insert(question);

            // 8. 生成三道相似题目
            log.info("🎯 生成相似题目...");
            List<Map<String, Object>> similarQuestions = generateThreeSimilarQuestions(ocrText, aiAnalysisResult);

            // 9. 创建初始对话消息
            createInitialChatMessages(session.getId(), userId, question, aiAnalysisResult);

            // 10. 构建完整响应
            result = buildIntegratedResult(question, session, aiAnalysisResult, similarQuestions,
                System.currentTimeMillis() - startTime);

            log.info("✅ 一体化搜题完成 - 题目ID: {}, 会话ID: {}, 耗时: {}ms",
                question.getId(), session.getId(), System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("❌ 一体化搜题处理失败", e);
            result.put("success", false);
            result.put("message", "搜题失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("processingTime", System.currentTimeMillis() - startTime);
        }

        return result;
    }

    /**
     * 基于搜题结果继续对话
     */
    @PostMapping("/continue-conversation")
    @ApiOperation(value = "继续对话", notes = "基于搜题结果继续与AI对话")
    public Map<String, Object> continueConversation(
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "会话ID", required = true) @RequestParam Long sessionId,
            @ApiParam(value = "用户消息", required = true) @RequestParam String message) {

        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            log.info("💬 继续对话 - 用户ID: {}, 会话ID: {}, 消息: {}", userId, sessionId, message);

            // 1. 验证会话是否存在
            TutorSession session = tutorSessionMapper.selectById(sessionId);
            if (session == null || !session.getUserId().equals(userId)) {
                result.put("success", false);
                result.put("message", "会话不存在或无权限访问");
                return result;
            }

            // 2. 获取对话历史
            List<TutorMessage> conversationHistory = tutorMessageMapper.getFullConversationBySessionId(sessionId);

            // 3. 保存用户消息
            TutorMessage userMessage = new TutorMessage()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setMessageType(TutorMessage.MESSAGE_TYPE_USER)
                .setContent(message)
                .setContentType(TutorMessage.CONTENT_TYPE_TEXT)
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now());
            tutorMessageMapper.insert(userMessage);

            // 4. 构建对话上下文
            String conversationContext = buildConversationContext(conversationHistory, message);

            // 5. 获取AI回复
            String aiResponse = aiChatService.chatWithContext(conversationContext);

            // 6. 保存AI回复
            TutorMessage aiMessage = new TutorMessage()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setMessageType(TutorMessage.MESSAGE_TYPE_AI)
                .setContent(aiResponse)
                .setContentType(TutorMessage.CONTENT_TYPE_TEXT)
                .setParentMessageId(userMessage.getId())
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now());
            tutorMessageMapper.insert(aiMessage);

            // 7. 更新会话最后消息时间
            tutorSessionMapper.updateLastMessageTime(sessionId, LocalDateTime.now());

            result.put("success", true);
            result.put("message", "对话成功");
            result.put("userMessage", userMessage);
            result.put("aiResponse", aiMessage);
            result.put("sessionId", sessionId);
            result.put("processingTime", System.currentTimeMillis() - startTime);

            log.info("✅ 对话完成 - 会话ID: {}, 耗时: {}ms", sessionId, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("❌ 对话处理失败", e);
            result.put("success", false);
            result.put("message", "对话失败: " + e.getMessage());
            result.put("processingTime", System.currentTimeMillis() - startTime);
        }

        return result;
    }

    /**
     * 构建缓存结果
     */
    private Map<String, Object> buildCachedResult(TutorQuestion cachedQuestion, Long userId, String sessionName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建或获取会话
            TutorSession session = createOrGetSession(userId, sessionName);

            // 构建AI分析结果
            Map<String, Object> aiAnalysisResult = new HashMap<>();
            aiAnalysisResult.put("subject", cachedQuestion.getSubject());
            aiAnalysisResult.put("questionType", cachedQuestion.getQuestionType());
            aiAnalysisResult.put("analysis", cachedQuestion.getAiAnalysis());
            aiAnalysisResult.put("solution", cachedQuestion.getSolutionSteps());
            aiAnalysisResult.put("answer", cachedQuestion.getFinalAnswer());
            aiAnalysisResult.put("confidence", cachedQuestion.getConfidenceScore());

            // 生成相似题目
            List<Map<String, Object>> similarQuestions = generateThreeSimilarQuestions(
                cachedQuestion.getOcrText(), aiAnalysisResult);

            result.put("success", true);
            result.put("message", "搜题成功（来自缓存）");
            result.put("cached", true);
            result.put("questionId", cachedQuestion.getId());
            result.put("sessionId", session.getId());
            result.put("ocrText", cachedQuestion.getOcrText());
            result.put("analysis", aiAnalysisResult);
            result.put("similarQuestions", similarQuestions);
            result.put("processingTime", 50); // 缓存响应很快

        } catch (Exception e) {
            log.error("构建缓存结果失败", e);
            result.put("success", false);
            result.put("message", "缓存结果处理失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从OCR结果中提取LaTeX公式
     */
    private String extractLatexFromOcrResult(JSONObject ocrJson) {
        try {
            if (ocrJson != null && ocrJson.containsKey("latex")) {
                return ocrJson.getString("latex");
            }
            // 如果没有专门的LaTeX字段，尝试从文本中提取数学公式
            return null;
        } catch (Exception e) {
            log.warn("提取LaTeX失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 执行综合AI分析
     */
    private Map<String, Object> performComprehensiveAnalysis(String ocrText, String ocrLatex) {
        Map<String, Object> analysisResult = new HashMap<>();

        try {
            // 1. 学科分类
            String subject = aiChatService.classifySubject(ocrText);

            // 2. 题型识别
            String questionType = aiChatService.identifyQuestionType(ocrText);

            // 3. 知识点提取
            String knowledgePoints = aiChatService.extractKnowledgePoints(ocrText);

            // 4. 难度评估
            Integer difficultyLevel = aiChatService.assessDifficulty(ocrText);

            // 5. 详细分析
            String detailedAnalysis = aiChatService.analyzeQuestion(ocrText);

            // 6. 解题步骤
            String solutionSteps = aiChatService.generateSolutionSteps(ocrText);

            // 7. 最终答案
            String finalAnswer = aiChatService.generateFinalAnswer(ocrText);

            // 8. 置信度评估
            BigDecimal confidence = aiChatService.calculateConfidence(ocrText, finalAnswer);

            analysisResult.put("subject", subject);
            analysisResult.put("questionType", questionType);
            analysisResult.put("knowledgePoints", knowledgePoints);
            analysisResult.put("difficultyLevel", difficultyLevel);
            analysisResult.put("analysis", detailedAnalysis);
            analysisResult.put("solutionSteps", solutionSteps);
            analysisResult.put("finalAnswer", finalAnswer);
            analysisResult.put("confidence", confidence);

            log.info("AI分析完成 - 学科: {}, 题型: {}, 难度: {}, 置信度: {}",
                subject, questionType, difficultyLevel, confidence);

        } catch (Exception e) {
            log.error("AI综合分析失败", e);
            // 提供默认值
            analysisResult.put("subject", "未知");
            analysisResult.put("questionType", "未知");
            analysisResult.put("analysis", "AI分析暂时不可用");
            analysisResult.put("solutionSteps", "请稍后重试");
            analysisResult.put("finalAnswer", "暂无答案");
            analysisResult.put("confidence", BigDecimal.valueOf(0.5));
        }

        return analysisResult;
    }

    /**
     * 创建或获取会话
     */
    private TutorSession createOrGetSession(Long userId, String sessionName) {
        try {
            // 如果没有提供会话名称，生成默认名称
            if (sessionName == null || sessionName.trim().isEmpty()) {
                sessionName = "搜题会话_" + LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("MMdd_HHmm"));
            }

            // 查找现有会话
            TutorSession existingSession = tutorSessionMapper.findByUserIdAndSessionName(userId, sessionName);
            if (existingSession != null) {
                log.info("找到现有会话: {}", existingSession.getId());
                return existingSession;
            }

            // 创建新会话
            TutorSession newSession = new TutorSession()
                .setUserId(userId)
                .setSessionName(sessionName)
                .setSessionType("QUESTION_SEARCH")
                .setStatus("ACTIVE")
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now())
                .setLastMessageTime(LocalDateTime.now());

            tutorSessionMapper.insert(newSession);
            log.info("创建新会话: {}", newSession.getId());

            return newSession;

        } catch (Exception e) {
            log.error("创建或获取会话失败", e);
            throw new RuntimeException("会话处理失败: " + e.getMessage());
        }
    }

    /**
     * 创建题目记录
     */
    private TutorQuestion createTutorQuestion(Long userId, Long sessionId, String imageUrl,
            String imageHash, String ocrText, String ocrLatex, Map<String, Object> aiAnalysisResult) {

        // 安全提取和截断字段值
        String subject = extractSubject((String) aiAnalysisResult.get("subject"));
        String questionType = extractQuestionType((String) aiAnalysisResult.get("questionType"));
        String knowledgePoints = truncateString((String) aiAnalysisResult.get("knowledgePoints"), 200);
        String analysis = truncateString((String) aiAnalysisResult.get("analysis"), 1000);
        String solutionSteps = truncateString((String) aiAnalysisResult.get("solutionSteps"), 2000);
        String finalAnswer = truncateString((String) aiAnalysisResult.get("finalAnswer"), 500);

        log.info("📝 字段提取结果 - 学科: {}, 题型: {}", subject, questionType);

        TutorQuestion question = new TutorQuestion()
            .setUserId(userId)
            .setSessionId(sessionId)
            .setImageUrl(imageUrl)
            .setImageHash(imageHash)
            .setOcrText(ocrText)
            .setOcrLatex(ocrLatex)
            .setSubject(subject)
            .setQuestionType(questionType)
            .setKnowledgePoints(knowledgePoints)
            .setDifficultyLevel((Integer) aiAnalysisResult.get("difficultyLevel"))
            .setAiAnalysis(analysis)
            .setSolutionSteps(solutionSteps)
            .setFinalAnswer(finalAnswer)
            .setConfidenceScore((BigDecimal) aiAnalysisResult.get("confidence"))
            .setProcessingTime(100) // 将在调用处设置实际值
            .setModelVersion("qwen-vl-plus-v1.0")
            .setIsWrong(false)
            .setMasteryStatus(0)
            .setReviewCount(0)
            .setCreatedTime(LocalDateTime.now())
            .setUpdatedTime(LocalDateTime.now());

        return question;
    }

    /**
     * 生成三道相似题目（完全基于AI生成，不查询数据库）
     */
    private List<Map<String, Object>> generateThreeSimilarQuestions(String originalText, Map<String, Object> analysisResult) {
        List<Map<String, Object>> similarQuestions = new ArrayList<>();

        try {
            log.info("🎯 开始AI生成相似题目...");

            // 构建更详细的AI提示词
            String prompt = buildEnhancedSimilarQuestionPrompt(originalText, analysisResult);

            // 调用AI生成相似题目
            String aiResponse = aiChatService.generateSimilarQuestions(prompt, 3);
            log.info("🤖 AI返回的相似题目: {}", aiResponse);

            // 解析AI返回的JSON
            try {
                List<Map<String, Object>> aiGeneratedQuestions = safeParseJsonArray(aiResponse);

                // 处理AI生成的题目
                for (int i = 0; i < Math.min(3, aiGeneratedQuestions.size()); i++) {
                    Map<String, Object> question = aiGeneratedQuestions.get(i);

                    // 添加元数据
                    question.put("questionId", "ai_similar_" + System.currentTimeMillis() + "_" + (i + 1));
                    question.put("difficulty", analysisResult.get("difficultyLevel"));
                    question.put("subject", analysisResult.get("subject"));
                    question.put("questionType", analysisResult.get("questionType"));
                    question.put("generatedBy", "AI");
                    question.put("generatedTime", LocalDateTime.now());
                    question.put("originalQuestionHash", originalText.hashCode());

                    similarQuestions.add(question);
                }

                log.info("✅ AI成功生成{}道相似题目", similarQuestions.size());

            } catch (Exception parseError) {
                log.warn("⚠️ 解析AI生成的相似题目失败，使用智能备用方案: {}", parseError.getMessage());
                similarQuestions = generateIntelligentFallbackQuestions(originalText, analysisResult);
            }

        } catch (Exception e) {
            log.error("❌ AI生成相似题目失败，使用智能备用方案", e);
            similarQuestions = generateIntelligentFallbackQuestions(originalText, analysisResult);
        }

        log.info("✅ 相似题目生成完成，共{}道（全部为AI生成，无数据库查询）", similarQuestions.size());
        return similarQuestions;
    }

    /**
     * 构建增强版相似题目生成提示词
     */
    private String buildEnhancedSimilarQuestionPrompt(String originalText, Map<String, Object> analysisResult) {
        return String.format("""
            你是一位专业的教育专家，请基于以下题目信息，创造性地生成3道相似但不重复的题目：

            【原题目】
            %s

            【题目分析】
            - 学科：%s
            - 题型：%s
            - 难度等级：%s/5
            - 知识点：%s

            【生成要求】
            1. 保持相同的学科、题型和难度等级
            2. 改变具体的数值、条件或情境，但保持核心知识点
            3. 确保每道题目都有独特性，避免简单的数字替换
            4. 题目要有实际教育价值和应用意义
            5. 提供完整的解答过程和思路

            【输出格式】
            请严格按照以下JSON数组格式返回，不要包含任何其他文字：
            [
              {
                "question": "第一道题目的完整描述",
                "answer": "第一道题目的最终答案",
                "explanation": "第一道题目的详细解题步骤和思路"
              },
              {
                "question": "第二道题目的完整描述",
                "answer": "第二道题目的最终答案",
                "explanation": "第二道题目的详细解题步骤和思路"
              },
              {
                "question": "第三道题目的完整描述",
                "answer": "第三道题目的最终答案",
                "explanation": "第三道题目的详细解题步骤和思路"
              }
            ]
            """,
            originalText,
            analysisResult.get("subject"),
            analysisResult.get("questionType"),
            analysisResult.get("difficultyLevel"),
            analysisResult.get("knowledgePoints")
        );
    }

    /**
     * 生成智能备用相似题目（当AI解析失败时使用）
     */
    private List<Map<String, Object>> generateIntelligentFallbackQuestions(String originalText, Map<String, Object> analysisResult) {
        List<Map<String, Object>> fallbackQuestions = new ArrayList<>();

        String subject = (String) analysisResult.get("subject");
        String questionType = (String) analysisResult.get("questionType");
        Integer difficulty = (Integer) analysisResult.get("difficultyLevel");

        log.info("🔄 使用智能备用方案生成相似题目 - 学科: {}, 题型: {}, 难度: {}", subject, questionType, difficulty);

        try {
            // 尝试使用简化的AI提示词重新生成
            String simplifiedPrompt = String.format("""
                基于题目"%s"，生成3道%s学科的%s类型题目。
                要求：
                1. 难度等级%d/5
                2. 改变数值但保持题型
                3. 只返回JSON数组：[{"question":"题目","answer":"答案","explanation":"解释"}]
                """,
                originalText.length() > 100 ? originalText.substring(0, 100) + "..." : originalText,
                subject, questionType, difficulty);

            String aiResponse = aiChatService.generateSimilarQuestions(simplifiedPrompt, 3);
            log.info("🤖 智能备用方案AI响应: {}", aiResponse);

            // 安全解析JSON
            List<Map<String, Object>> aiQuestions = safeParseJsonArray(aiResponse);

            for (int i = 0; i < Math.min(3, aiQuestions.size()); i++) {
                Map<String, Object> question = aiQuestions.get(i);
                question.put("questionId", "fallback_ai_" + System.currentTimeMillis() + "_" + (i + 1));
                question.put("difficulty", difficulty);
                question.put("subject", subject);
                question.put("generatedBy", "AI_FALLBACK");
                fallbackQuestions.add(question);
            }

            log.info("✅ 智能备用方案成功生成{}道题目", fallbackQuestions.size());

        } catch (Exception e) {
            log.warn("⚠️ 智能备用方案也失败，使用模板生成", e);
            fallbackQuestions = generateTemplateFallbackQuestions(originalText, analysisResult);
        }

        return fallbackQuestions;
    }

    /**
     * 生成模板备用相似题目（最后的备用方案）
     */
    private List<Map<String, Object>> generateTemplateFallbackQuestions(String originalText, Map<String, Object> analysisResult) {
        List<Map<String, Object>> templateQuestions = new ArrayList<>();

        String subject = (String) analysisResult.get("subject");
        Integer difficulty = (Integer) analysisResult.get("difficultyLevel");

        for (int i = 1; i <= 3; i++) {
            Map<String, Object> question = new HashMap<>();
            question.put("questionId", "template_" + System.currentTimeMillis() + "_" + i);
            question.put("question", generateTemplateQuestion(originalText, subject, i));
            question.put("answer", "请根据题目要求进行计算和分析");
            question.put("explanation", String.format("这是一道%s相关的练习题，请运用相同的知识点和解题方法进行求解", subject));
            question.put("difficulty", difficulty);
            question.put("subject", subject);
            question.put("generatedBy", "TEMPLATE");
            question.put("generatedTime", LocalDateTime.now());

            templateQuestions.add(question);
        }

        log.info("📝 模板备用方案生成{}道题目", templateQuestions.size());
        return templateQuestions;
    }

    /**
     * 生成模板题目内容
     */
    private String generateTemplateQuestion(String originalText, String subject, int index) {
        if (originalText == null || originalText.trim().isEmpty()) {
            return String.format("这是一道%s练习题 %d", subject, index);
        }

        // 基于原题目生成变化
        String[] templates = {
            "参考题目：" + (originalText.length() > 50 ? originalText.substring(0, 50) + "..." : originalText),
            "类似问题：请运用相同方法解决类似的" + subject + "问题",
            "举一反三：基于上述题目，请思考相关的" + subject + "应用"
        };

        return templates[(index - 1) % templates.length];
    }

    /**
     * 安全截断字符串，防止数据库字段长度超限
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) {
            return null;
        }

        // 移除可能的格式标记和多余空白
        String cleaned = str.trim()
            .replaceAll("【[^】]*】[：:]?", "") // 移除【标题】：格式
            .replaceAll("\\*\\*([^*]+)\\*\\*", "$1") // 移除**粗体**格式
            .replaceAll("\\n+", " ") // 将换行符替换为空格
            .replaceAll("\\s+", " ") // 合并多个空格
            .trim();

        if (cleaned.length() <= maxLength) {
            return cleaned;
        }

        // 截断并添加省略号
        return cleaned.substring(0, maxLength - 3) + "...";
    }

    /**
     * 智能提取简洁的题型信息
     */
    private String extractQuestionType(String rawQuestionType) {
        if (rawQuestionType == null || rawQuestionType.trim().isEmpty()) {
            return "未知题型";
        }

        String cleaned = rawQuestionType.trim();

        // 常见题型关键词
        String[] questionTypes = {
            "选择题", "填空题", "计算题", "证明题", "应用题", "解答题",
            "判断题", "简答题", "分析题", "综合题", "实验题", "作图题"
        };

        // 查找匹配的题型
        for (String type : questionTypes) {
            if (cleaned.contains(type)) {
                return type;
            }
        }

        // 如果没有找到标准题型，提取第一个有意义的词
        String[] words = cleaned.split("[\\s，,。.：:]");
        for (String word : words) {
            word = word.trim();
            if (word.length() >= 2 && word.length() <= 10 && !word.matches(".*[【】\\[\\]].*")) {
                return word;
            }
        }

        // 最后的备用方案
        return truncateString(cleaned, 20);
    }

    /**
     * 智能提取学科信息
     */
    private String extractSubject(String rawSubject) {
        if (rawSubject == null || rawSubject.trim().isEmpty()) {
            return "未知";
        }

        String cleaned = rawSubject.trim();

        // 常见学科关键词
        String[] subjects = {
            "数学", "语文", "英语", "物理", "化学", "生物",
            "历史", "地理", "政治", "科学", "计算机"
        };

        // 查找匹配的学科
        for (String subject : subjects) {
            if (cleaned.contains(subject)) {
                return subject;
            }
        }

        // 如果没有找到标准学科，返回截断的原始内容
        return truncateString(cleaned, 20);
    }

    /**
     * 提取和清理JSON内容
     */
    private String extractAndCleanJson(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "[]";
        }

        try {
            // 移除可能的前后缀文本，只保留JSON部分
            String cleaned = aiResponse.trim();

            // 查找JSON数组的开始和结束
            int startIndex = cleaned.indexOf('[');
            int endIndex = cleaned.lastIndexOf(']');

            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                cleaned = cleaned.substring(startIndex, endIndex + 1);
            } else {
                // 如果没有找到数组，查找JSON对象
                startIndex = cleaned.indexOf('{');
                endIndex = cleaned.lastIndexOf('}');

                if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                    // 将单个对象包装成数组
                    cleaned = "[" + cleaned.substring(startIndex, endIndex + 1) + "]";
                } else {
                    log.warn("⚠️ 无法从AI响应中提取有效JSON，返回空数组");
                    return "[]";
                }
            }

            // 清理常见的JSON格式问题
            cleaned = cleaned
                .replaceAll("```json", "")
                .replaceAll("```", "")
                .replaceAll("\\n", "")
                .replaceAll("\\r", "")
                .trim();

            // 验证JSON格式
            JSON.parseArray(cleaned);

            return cleaned;

        } catch (Exception e) {
            log.warn("⚠️ JSON清理失败: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * 安全的JSON解析方法
     */
    private List<Map<String, Object>> safeParseJsonArray(String jsonString) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                return result;
            }

            String cleanedJson = extractAndCleanJson(jsonString);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> parsed = JSON.parseArray(cleanedJson)
                .stream()
                .map(obj -> (Map<String, Object>) obj)
                .collect(Collectors.toList());

            return parsed;

        } catch (Exception e) {
            log.warn("⚠️ JSON解析失败: {}, 原始内容: {}", e.getMessage(), jsonString);
            return result;
        }
    }

    /**
     * 生成默认相似题目（当AI生成失败时使用）
     */
    private List<Map<String, Object>> generateDefaultSimilarQuestions(String originalText, Map<String, Object> analysisResult) {
        List<Map<String, Object>> defaultQuestions = new ArrayList<>();

        String subject = (String) analysisResult.get("subject");

        if ("数学".equals(subject)) {
            defaultQuestions.add(createDefaultMathQuestion(1));
            defaultQuestions.add(createDefaultMathQuestion(2));
            defaultQuestions.add(createDefaultMathQuestion(3));
        } else if ("物理".equals(subject)) {
            defaultQuestions.add(createDefaultPhysicsQuestion(1));
            defaultQuestions.add(createDefaultPhysicsQuestion(2));
            defaultQuestions.add(createDefaultPhysicsQuestion(3));
        } else {
            // 通用题目
            defaultQuestions.add(createDefaultGenericQuestion(1, subject));
            defaultQuestions.add(createDefaultGenericQuestion(2, subject));
            defaultQuestions.add(createDefaultGenericQuestion(3, subject));
        }

        return defaultQuestions;
    }

    /**
     * 创建默认数学题目
     */
    private Map<String, Object> createDefaultMathQuestion(int index) {
        Map<String, Object> question = new HashMap<>();
        switch (index) {
            case 1:
                question.put("question", "求解方程：3x + 5 = 14");
                question.put("answer", "x = 3");
                question.put("explanation", "移项得：3x = 14 - 5 = 9，所以 x = 9 ÷ 3 = 3");
                break;
            case 2:
                question.put("question", "计算：(2x + 1)(x - 3) = 0 的解");
                question.put("answer", "x = -1/2 或 x = 3");
                question.put("explanation", "根据零乘积性质，2x + 1 = 0 或 x - 3 = 0");
                break;
            case 3:
                question.put("question", "已知函数 f(x) = 2x + 1，求 f(3) 的值");
                question.put("answer", "f(3) = 7");
                question.put("explanation", "将 x = 3 代入函数：f(3) = 2×3 + 1 = 7");
                break;
        }
        question.put("questionId", "default_math_" + index);
        question.put("subject", "数学");
        return question;
    }

    /**
     * 创建默认物理题目
     */
    private Map<String, Object> createDefaultPhysicsQuestion(int index) {
        Map<String, Object> question = new HashMap<>();
        switch (index) {
            case 1:
                question.put("question", "一个物体从静止开始，以2m/s²的加速度运动，求3秒后的速度");
                question.put("answer", "v = 6 m/s");
                question.put("explanation", "根据公式 v = v₀ + at，v = 0 + 2×3 = 6 m/s");
                break;
            case 2:
                question.put("question", "计算重力为10N的物体在地面上的质量");
                question.put("answer", "m = 1 kg");
                question.put("explanation", "根据 G = mg，m = G/g = 10/10 = 1 kg");
                break;
            case 3:
                question.put("question", "一个弹簧的弹性系数为100N/m，压缩0.1m时的弹力是多少？");
                question.put("answer", "F = 10 N");
                question.put("explanation", "根据胡克定律 F = kx，F = 100×0.1 = 10 N");
                break;
        }
        question.put("questionId", "default_physics_" + index);
        question.put("subject", "物理");
        return question;
    }

    /**
     * 创建默认通用题目
     */
    private Map<String, Object> createDefaultGenericQuestion(int index, String subject) {
        Map<String, Object> question = new HashMap<>();
        question.put("question", String.format("这是一道%s相关的练习题目 %d", subject, index));
        question.put("answer", "请参考教材相关章节");
        question.put("explanation", "建议复习相关知识点后再次尝试");
        question.put("questionId", "default_generic_" + index);
        question.put("subject", subject);
        return question;
    }

    /**
     * 创建初始对话消息
     */
    private void createInitialChatMessages(Long sessionId, Long userId, TutorQuestion question, Map<String, Object> analysisResult) {
        try {
            // 创建用户的初始消息（题目图片）
            TutorMessage userMessage = new TutorMessage()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setMessageType(TutorMessage.MESSAGE_TYPE_USER)
                .setContent("我上传了一道题目，请帮我分析解答")
                .setContentType(TutorMessage.CONTENT_TYPE_IMAGE)
                .setMetadata(JSON.toJSONString(Map.of(
                    "imageUrl", question.getImageUrl(),
                    "ocrText", question.getOcrText()
                )))
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now());
            tutorMessageMapper.insert(userMessage);

            // 创建AI的分析回复
            String aiResponse = buildInitialAiResponse(analysisResult);
            TutorMessage aiMessage = new TutorMessage()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setMessageType(TutorMessage.MESSAGE_TYPE_AI)
                .setContent(aiResponse)
                .setContentType(TutorMessage.CONTENT_TYPE_TEXT)
                .setParentMessageId(userMessage.getId())
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now());
            tutorMessageMapper.insert(aiMessage);

            log.info("✅ 创建初始对话消息完成");

        } catch (Exception e) {
            log.error("创建初始对话消息失败", e);
        }
    }

    /**
     * 构建初始AI回复
     */
    private String buildInitialAiResponse(Map<String, Object> analysisResult) {
        StringBuilder response = new StringBuilder();
        response.append("我已经分析了您的题目，以下是详细解答：\n\n");

        response.append("📚 **学科分类**：").append(analysisResult.get("subject")).append("\n");
        response.append("🏷️ **题目类型**：").append(analysisResult.get("questionType")).append("\n");
        response.append("⭐ **难度等级**：").append(analysisResult.get("difficultyLevel")).append("/5\n\n");

        response.append("🔍 **题目分析**：\n").append(analysisResult.get("analysis")).append("\n\n");

        response.append("📝 **解题步骤**：\n").append(analysisResult.get("solutionSteps")).append("\n\n");

        response.append("✅ **最终答案**：").append(analysisResult.get("finalAnswer")).append("\n\n");

        response.append("💡 **小提示**：如果您对解题过程有任何疑问，可以继续提问，我会为您详细解释！");

        return response.toString();
    }

    /**
     * 构建完整的一体化结果
     */
    private Map<String, Object> buildIntegratedResult(TutorQuestion question, TutorSession session,
            Map<String, Object> analysisResult, List<Map<String, Object>> similarQuestions, long processingTime) {

        Map<String, Object> result = new HashMap<>();

        result.put("success", true);
        result.put("message", "一体化搜题完成");
        result.put("cached", false);

        // 基本信息
        result.put("questionId", question.getId());
        result.put("sessionId", session.getId());
        result.put("sessionName", session.getSessionName());

        // OCR结果
        result.put("ocrText", question.getOcrText());
        result.put("ocrLatex", question.getOcrLatex());
        result.put("imageUrl", question.getImageUrl());

        // AI分析结果
        result.put("analysis", analysisResult);

        // 相似题目
        result.put("similarQuestions", similarQuestions);

        // 对话功能提示
        result.put("chatEnabled", true);
        result.put("chatInstructions", "您可以使用 /continue-conversation 接口继续与AI对话");

        // 性能信息
        result.put("processingTime", processingTime);
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    /**
     * 构建对话上下文
     */
    private String buildConversationContext(List<TutorMessage> conversationHistory, String newMessage) {
        StringBuilder context = new StringBuilder();
        context.append("以下是我们之前的对话历史：\n\n");

        for (TutorMessage message : conversationHistory) {
            if (TutorMessage.MESSAGE_TYPE_USER.equals(message.getMessageType())) {
                context.append("用户：").append(message.getContent()).append("\n");
            } else if (TutorMessage.MESSAGE_TYPE_AI.equals(message.getMessageType())) {
                context.append("AI：").append(message.getContent()).append("\n");
            }
        }

        context.append("\n现在用户的新问题是：").append(newMessage);
        context.append("\n\n请基于上述对话历史，回答用户的新问题。保持回答的连贯性和相关性。");

        return context.toString();
    }

    /**
     * 从OCR结果中提取文字
     */
    private String extractTextFromOcrResult(JSONObject ocrJson) {
        try {
            if (ocrJson == null) {
                return "";
            }

            // 尝试不同的OCR结果格式
            if (ocrJson.containsKey("words_result")) {
                // 百度OCR格式
                return ocrJson.getJSONArray("words_result")
                    .stream()
                    .map(item -> ((JSONObject) item).getString("words"))
                    .collect(Collectors.joining("\n"));
            } else if (ocrJson.containsKey("text")) {
                // 通用文本格式
                return ocrJson.getString("text");
            } else if (ocrJson.containsKey("content")) {
                // 阿里云OCR格式
                return ocrJson.getString("content");
            } else {
                // 直接返回JSON字符串
                return ocrJson.toString();
            }
        } catch (Exception e) {
            log.error("提取OCR文字失败", e);
            return "OCR识别失败";
        }
    }

    /**
     * 基于搜题结果继续对话
     */
    @PostMapping("/continue-chat")
    @ApiOperation(value = "基于搜题结果继续对话", notes = "在搜题结果基础上继续与AI对话")
    public Map<String, Object> continueChat(
            @RequestParam Long userId,
            @RequestParam Long questionId,
            @RequestParam String message) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔄 基于搜题结果继续对话 - 用户ID: {}, 题目ID: {}, 消息: {}", userId, questionId, message);

            // 获取题目信息
            QuestionRecord question = questionRecordMapper.selectById(questionId);
            if (question == null) {
                result.put("success", false);
                result.put("message", "题目不存在");
                return result;
            }

            // 构建包含题目信息的上下文
            String contextInfo = buildQuestionContext(question);
            String fullMessage = contextInfo + "\n\n用户问题：" + message + "\n\n" +
                "请基于上述题目信息，详细回答用户的问题。";

            // 调用AI服务获取回复
            String response = aiChatService.chat(fullMessage);

            // 如果用户询问相似题目，生成新的相似题目
            List<Map<String, Object>> newSimilarQuestions = new ArrayList<>();
            if (message.contains("相似") || message.contains("类似") || message.contains("举一反三")) {
                newSimilarQuestions = generateSimilarQuestions(question.getQuestionText());
            }

            result.put("success", true);
            result.put("message", "对话成功");
            result.put("data", Map.of(
                "questionId", questionId,
                "userMessage", message,
                "aiResponse", response,
                "questionInfo", buildQuestionResult(question),
                "newSimilarQuestions", newSimilarQuestions,
                "timestamp", System.currentTimeMillis()
            ));

            log.info("✅ 基于搜题结果的对话完成 - 用户ID: {}", userId);

        } catch (Exception e) {
            log.error("❌ 基于搜题结果的对话失败 - 用户ID: {}, 题目ID: {}", userId, questionId, e);
            result.put("success", false);
            result.put("message", "对话失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * AI对话接口 - 优化版
     */
    @PostMapping("/chat")
    @ApiOperation(value = "AI对话", notes = "与AI进行自然语言对话，支持上下文理解")
    public Map<String, Object> chat(
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "用户消息", required = true) @RequestParam String message) {

        long startTime = System.currentTimeMillis();

        try {
            log.info("💬 AI对话请求 - 用户ID: {}, 消息长度: {}", userId, message.length());

            // 参数验证
            if (message == null || message.trim().isEmpty()) {
                return buildErrorResponse("消息内容不能为空", "PARAM_ERROR");
            }

            if (message.length() > 1000) {
                return buildErrorResponse("消息长度不能超过1000字符", "MESSAGE_TOO_LONG");
            }

            // 调用AI服务
            String response = aiChatService.chat(message.trim());

            // 构建成功响应
            return buildSuccessResponse("对话成功", Map.of(
                "userMessage", message.trim(),
                "aiResponse", response,
                "userId", userId,
                "processingTime", System.currentTimeMillis() - startTime
            ));

        } catch (Exception e) {
            log.error("❌ AI对话失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return buildErrorResponse("对话失败: " + e.getMessage(), "AI_CHAT_ERROR");
        }
    }

    /**
     * 获取用户的会话列表
     */
    @GetMapping("/sessions/{userId}")
    @ApiOperation(value = "获取用户会话列表", notes = "获取指定用户的所有活跃会话")
    public Map<String, Object> getUserSessions(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "每页数量", required = false) @RequestParam(defaultValue = "20") Integer size) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("📋 获取用户会话列表 - 用户ID: {}, 页码: {}, 每页: {}", userId, page, size);

            // 获取用户的活跃会话列表
            List<TutorSession> sessions = tutorSessionMapper.getActiveSessionsByUserId(userId);

            // 为每个会话添加统计信息
            List<Map<String, Object>> sessionList = new ArrayList<>();
            for (TutorSession session : sessions) {
                Map<String, Object> sessionInfo = buildSessionInfo(session);
                sessionList.add(sessionInfo);
            }

            result.put("success", true);
            result.put("message", "获取会话列表成功");
            result.put("sessions", sessionList);
            result.put("total", sessionList.size());
            result.put("page", page);
            result.put("size", size);

            log.info("✅ 获取会话列表成功 - 共{}个会话", sessionList.size());

        } catch (Exception e) {
            log.error("❌ 获取会话列表失败", e);
            result.put("success", false);
            result.put("message", "获取会话列表失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取会话详细信息和对话记录
     */
    @GetMapping("/session/{sessionId}/messages")
    @ApiOperation(value = "获取会话对话记录", notes = "获取指定会话的完整对话记录")
    public Map<String, Object> getSessionMessages(
            @ApiParam(value = "会话ID", required = true) @PathVariable Long sessionId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "每页数量", required = false) @RequestParam(defaultValue = "50") Integer size) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("💬 获取会话对话记录 - 会话ID: {}, 用户ID: {}", sessionId, userId);

            // 验证会话是否存在且属于该用户
            TutorSession session = tutorSessionMapper.selectById(sessionId);
            if (session == null || !session.getUserId().equals(userId)) {
                result.put("success", false);
                result.put("message", "会话不存在或无权限访问");
                return result;
            }

            // 获取对话记录
            int offset = (page - 1) * size;
            List<TutorMessage> messages = tutorMessageMapper.getMessagesBySessionId(sessionId, offset, size);

            // 获取会话相关的题目信息
            List<TutorQuestion> questions = tutorQuestionMapper.getQuestionsBySessionId(sessionId);

            // 构建响应数据
            Map<String, Object> sessionInfo = buildDetailedSessionInfo(session, questions);
            List<Map<String, Object>> messageList = buildMessageList(messages);

            result.put("success", true);
            result.put("message", "获取对话记录成功");
            result.put("sessionInfo", sessionInfo);
            result.put("messages", messageList);
            result.put("messageCount", tutorMessageMapper.countMessagesBySessionId(sessionId));
            result.put("page", page);
            result.put("size", size);

            log.info("✅ 获取对话记录成功 - 会话: {}, 消息数: {}", session.getSessionName(), messageList.size());

        } catch (Exception e) {
            log.error("❌ 获取对话记录失败", e);
            result.put("success", false);
            result.put("message", "获取对话记录失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 恢复到指定会话
     */
    @PostMapping("/session/{sessionId}/resume")
    @ApiOperation(value = "恢复会话", notes = "恢复到指定会话，可以继续对话")
    public Map<String, Object> resumeSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable Long sessionId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔄 恢复会话 - 会话ID: {}, 用户ID: {}", sessionId, userId);

            // 验证会话是否存在且属于该用户
            TutorSession session = tutorSessionMapper.selectById(sessionId);
            if (session == null || !session.getUserId().equals(userId)) {
                result.put("success", false);
                result.put("message", "会话不存在或无权限访问");
                return result;
            }

            // 更新会话状态为活跃
            session.setStatus("ACTIVE");
            session.setUpdatedTime(LocalDateTime.now());
            tutorSessionMapper.updateById(session);

            // 获取会话的最新对话记录（最近10条）
            List<TutorMessage> recentMessages = tutorMessageMapper.getMessagesBySessionId(sessionId, 0, 10);

            // 获取会话相关的题目
            List<TutorQuestion> questions = tutorQuestionMapper.getQuestionsBySessionId(sessionId);

            // 构建会话上下文
            String sessionContext = buildSessionResumeContext(session, questions, recentMessages);

            result.put("success", true);
            result.put("message", "会话恢复成功");
            result.put("sessionId", sessionId);
            result.put("sessionName", session.getSessionName());
            result.put("sessionContext", sessionContext);
            result.put("recentMessages", buildMessageList(recentMessages));
            result.put("questions", buildQuestionSummaryList(questions));
            result.put("canContinueChat", true);

            log.info("✅ 会话恢复成功 - 会话: {}", session.getSessionName());

        } catch (Exception e) {
            log.error("❌ 恢复会话失败", e);
            result.put("success", false);
            result.put("message", "恢复会话失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/session/{sessionId}")
    @ApiOperation(value = "删除会话", notes = "删除指定会话及其所有相关数据")
    public Map<String, Object> deleteSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable Long sessionId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🗑️ 删除会话 - 会话ID: {}, 用户ID: {}", sessionId, userId);

            // 验证会话是否存在且属于该用户
            TutorSession session = tutorSessionMapper.selectById(sessionId);
            if (session == null || !session.getUserId().equals(userId)) {
                result.put("success", false);
                result.put("message", "会话不存在或无权限访问");
                return result;
            }

            // 软删除会话（设置状态为DELETED）
            session.setStatus("DELETED");
            session.setUpdatedTime(LocalDateTime.now());
            tutorSessionMapper.updateById(session);

            // 软删除相关消息
            List<TutorMessage> messages = tutorMessageMapper.getFullConversationBySessionId(sessionId);
            for (TutorMessage message : messages) {
                message.setIsDeleted(true);
                message.setUpdatedTime(LocalDateTime.now());
                tutorMessageMapper.updateById(message);
            }

            result.put("success", true);
            result.put("message", "会话删除成功");
            result.put("deletedSessionId", sessionId);
            result.put("deletedSessionName", session.getSessionName());

            log.info("✅ 会话删除成功 - 会话: {}", session.getSessionName());

        } catch (Exception e) {
            log.error("❌ 删除会话失败", e);
            result.put("success", false);
            result.put("message", "删除会话失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 测试创建会话接口（用于调试）
     */
    @PostMapping("/test-create-session")
    @ApiOperation(value = "测试创建会话", notes = "用于调试的测试接口")
    public Map<String, Object> testCreateSession(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String sessionName = (String) request.get("sessionName");
            String sessionType = (String) request.getOrDefault("sessionType", "QUESTION_SEARCH");

            log.info("🧪 测试创建会话 - 用户ID: {}, 会话名称: {}", userId, sessionName);

            // 创建测试会话
            TutorSession session = new TutorSession()
                .setUserId(userId)
                .setSessionName(sessionName)
                .setSessionType(sessionType)
                .setStatus("ACTIVE")
                .setCreatedTime(LocalDateTime.now())
                .setUpdatedTime(LocalDateTime.now())
                .setLastMessageTime(LocalDateTime.now());

            int insertResult = tutorSessionMapper.insert(session);

            if (insertResult > 0) {
                result.put("success", true);
                result.put("message", "测试会话创建成功");
                result.put("sessionId", session.getId());
                result.put("sessionName", session.getSessionName());

                // 创建一条测试消息
                TutorMessage testMessage = new TutorMessage()
                    .setSessionId(session.getId())
                    .setUserId(userId)
                    .setMessageType(TutorMessage.MESSAGE_TYPE_USER)
                    .setContent("这是一条测试消息")
                    .setContentType(TutorMessage.CONTENT_TYPE_TEXT)
                    .setCreatedTime(LocalDateTime.now())
                    .setUpdatedTime(LocalDateTime.now());
                tutorMessageMapper.insert(testMessage);

                log.info("✅ 测试会话创建成功 - 会话ID: {}", session.getId());
            } else {
                result.put("success", false);
                result.put("message", "会话创建失败");
            }

        } catch (Exception e) {
            log.error("❌ 测试创建会话失败", e);
            result.put("success", false);
            result.put("message", "创建失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }



    // ==================== 辅助方法 ====================

    /**
     * 构建会话信息
     */
    private Map<String, Object> buildSessionInfo(TutorSession session) {
        Map<String, Object> sessionInfo = new HashMap<>();

        try {
            sessionInfo.put("sessionId", session.getId());
            sessionInfo.put("sessionName", session.getSessionName());
            sessionInfo.put("sessionType", session.getSessionType());
            sessionInfo.put("status", session.getStatus());
            sessionInfo.put("createdTime", session.getCreatedTime());
            sessionInfo.put("lastMessageTime", session.getLastMessageTime());

            // 获取会话统计信息
            Integer messageCount = tutorMessageMapper.countMessagesBySessionId(session.getId());
            List<TutorQuestion> questions = tutorQuestionMapper.getQuestionsBySessionId(session.getId());
            TutorMessage lastMessage = tutorMessageMapper.getLastMessageBySessionId(session.getId());

            sessionInfo.put("messageCount", messageCount);
            sessionInfo.put("questionCount", questions.size());
            sessionInfo.put("lastMessage", lastMessage != null ? lastMessage.getContent() : "暂无消息");
            sessionInfo.put("lastMessageTime", lastMessage != null ? lastMessage.getCreatedTime() : session.getCreatedTime());

            // 计算会话活跃度
            long daysSinceLastMessage = java.time.Duration.between(
                session.getLastMessageTime() != null ? session.getLastMessageTime() : session.getCreatedTime(),
                LocalDateTime.now()
            ).toDays();
            sessionInfo.put("daysSinceLastMessage", daysSinceLastMessage);
            sessionInfo.put("isActive", daysSinceLastMessage <= 7); // 7天内有活动算活跃

        } catch (Exception e) {
            log.error("构建会话信息失败", e);
        }

        return sessionInfo;
    }

    /**
     * 构建详细会话信息
     */
    private Map<String, Object> buildDetailedSessionInfo(TutorSession session, List<TutorQuestion> questions) {
        Map<String, Object> sessionInfo = buildSessionInfo(session);

        try {
            // 添加题目详细信息
            List<Map<String, Object>> questionDetails = new ArrayList<>();
            for (TutorQuestion question : questions) {
                Map<String, Object> questionInfo = new HashMap<>();
                questionInfo.put("questionId", question.getId());
                questionInfo.put("ocrText", question.getOcrText());
                questionInfo.put("subject", question.getSubject());
                questionInfo.put("questionType", question.getQuestionType());
                questionInfo.put("difficultyLevel", question.getDifficultyLevel());
                questionInfo.put("finalAnswer", question.getFinalAnswer());
                questionInfo.put("createdTime", question.getCreatedTime());
                questionInfo.put("imageUrl", question.getImageUrl());
                questionDetails.add(questionInfo);
            }
            sessionInfo.put("questionDetails", questionDetails);

            // 添加会话摘要
            if (!questions.isEmpty()) {
                Map<String, Long> subjectCount = questions.stream()
                    .collect(Collectors.groupingBy(
                        q -> q.getSubject() != null ? q.getSubject() : "未知",
                        Collectors.counting()
                    ));
                sessionInfo.put("subjectDistribution", subjectCount);

                double avgDifficulty = questions.stream()
                    .filter(q -> q.getDifficultyLevel() != null)
                    .mapToInt(TutorQuestion::getDifficultyLevel)
                    .average()
                    .orElse(0.0);
                sessionInfo.put("averageDifficulty", Math.round(avgDifficulty * 10.0) / 10.0);
            }

        } catch (Exception e) {
            log.error("构建详细会话信息失败", e);
        }

        return sessionInfo;
    }

    /**
     * 构建消息列表
     */
    private List<Map<String, Object>> buildMessageList(List<TutorMessage> messages) {
        List<Map<String, Object>> messageList = new ArrayList<>();

        try {
            for (TutorMessage message : messages) {
                Map<String, Object> messageInfo = new HashMap<>();
                messageInfo.put("messageId", message.getId());
                messageInfo.put("messageType", message.getMessageType());
                messageInfo.put("content", message.getContent());
                messageInfo.put("contentType", message.getContentType());
                messageInfo.put("createdTime", message.getCreatedTime());
                messageInfo.put("parentMessageId", message.getParentMessageId());

                // 解析元数据
                if (message.getMetadata() != null) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> metadata = JSON.parseObject(message.getMetadata(), Map.class);
                        messageInfo.put("metadata", metadata);
                    } catch (Exception e) {
                        messageInfo.put("metadata", null);
                    }
                }

                messageList.add(messageInfo);
            }
        } catch (Exception e) {
            log.error("构建消息列表失败", e);
        }

        return messageList;
    }

    /**
     * 构建会话恢复上下文
     */
    private String buildSessionResumeContext(TutorSession session, List<TutorQuestion> questions, List<TutorMessage> recentMessages) {
        StringBuilder context = new StringBuilder();

        try {
            context.append("=== 会话恢复信息 ===\n");
            context.append("会话名称：").append(session.getSessionName()).append("\n");
            context.append("创建时间：").append(session.getCreatedTime()).append("\n");
            context.append("最后活动：").append(session.getLastMessageTime()).append("\n\n");

            // 添加题目摘要
            if (!questions.isEmpty()) {
                context.append("=== 题目摘要 ===\n");
                for (int i = 0; i < Math.min(3, questions.size()); i++) {
                    TutorQuestion question = questions.get(i);
                    context.append(String.format("%d. %s (%s)\n",
                        i + 1,
                        question.getOcrText().length() > 50 ?
                            question.getOcrText().substring(0, 50) + "..." : question.getOcrText(),
                        question.getSubject()));
                }
                if (questions.size() > 3) {
                    context.append("... 还有").append(questions.size() - 3).append("道题目\n");
                }
                context.append("\n");
            }

            // 添加最近对话
            if (!recentMessages.isEmpty()) {
                context.append("=== 最近对话 ===\n");
                for (TutorMessage message : recentMessages) {
                    String role = TutorMessage.MESSAGE_TYPE_USER.equals(message.getMessageType()) ? "用户" : "AI";
                    String content = message.getContent().length() > 100 ?
                        message.getContent().substring(0, 100) + "..." : message.getContent();
                    context.append(role).append("：").append(content).append("\n");
                }
                context.append("\n");
            }

            context.append("您现在可以继续这个会话的对话。");

        } catch (Exception e) {
            log.error("构建会话恢复上下文失败", e);
            context.append("会话恢复成功，您可以继续对话。");
        }

        return context.toString();
    }

    /**
     * 构建题目摘要列表
     */
    private List<Map<String, Object>> buildQuestionSummaryList(List<TutorQuestion> questions) {
        List<Map<String, Object>> summaryList = new ArrayList<>();

        try {
            for (TutorQuestion question : questions) {
                Map<String, Object> summary = new HashMap<>();
                summary.put("questionId", question.getId());
                summary.put("ocrText", question.getOcrText().length() > 100 ?
                    question.getOcrText().substring(0, 100) + "..." : question.getOcrText());
                summary.put("subject", question.getSubject());
                summary.put("questionType", question.getQuestionType());
                summary.put("difficultyLevel", question.getDifficultyLevel());
                summary.put("finalAnswer", question.getFinalAnswer());
                summary.put("createdTime", question.getCreatedTime());
                summaryList.add(summary);
            }
        } catch (Exception e) {
            log.error("构建题目摘要列表失败", e);
        }

        return summaryList;
    }

    /**
     * 构建题目结果
     */
    private Map<String, Object> buildQuestionResult(QuestionRecord question) {
        Map<String, Object> result = new HashMap<>();
        result.put("questionId", question.getId());
        result.put("questionText", question.getQuestionText());
        result.put("questionType", question.getQuestionType());
        result.put("subject", question.getSubject());
        result.put("analysis", question.getAnalysis());
        result.put("solutionSteps", question.getSolutionSteps());
        result.put("finalAnswer", question.getFinalAnswer());
        result.put("imagePath", question.getImagePath());
        result.put("createTime", question.getCreateTime());
        return result;
    }

    /**
     * 构建题目上下文信息
     */
    private String buildQuestionContext(QuestionRecord question) {
        StringBuilder context = new StringBuilder();
        context.append("=== 题目信息 ===\n");
        context.append("题目内容：").append(question.getQuestionText()).append("\n");
        if (question.getSubject() != null) {
            context.append("学科：").append(question.getSubject()).append("\n");
        }
        if (question.getQuestionType() != null) {
            context.append("题型：").append(question.getQuestionType()).append("\n");
        }
        if (question.getAnalysis() != null) {
            context.append("AI分析：").append(question.getAnalysis()).append("\n");
        }
        context.append("=== 题目信息结束 ===\n");
        return context.toString();
    }

    /**
     * 生成相似题目
     */
    private List<Map<String, Object>> generateSimilarQuestions(String originalText) {
        List<Map<String, Object>> similarQuestions = new ArrayList<>();

        try {
            // 使用AI服务生成相似题目
            String similarQuestionsJson = aiChatService.generateSimilarQuestions(originalText, 3);

            // 尝试解析AI返回的JSON
            try {
                List<Map<String, Object>> aiGeneratedQuestions = safeParseJsonArray(similarQuestionsJson);

                for (int i = 0; i < aiGeneratedQuestions.size() && i < 3; i++) {
                    Map<String, Object> aiQuestion = aiGeneratedQuestions.get(i);
                    Map<String, Object> similar = new HashMap<>();

                    similar.put("id", System.currentTimeMillis() + i);
                    similar.put("questionText", aiQuestion.get("question"));
                    similar.put("answer", aiQuestion.get("answer"));
                    similar.put("explanation", aiQuestion.get("explanation"));
                    similar.put("generatedBy", "AI");

                    similarQuestions.add(similar);
                }

            } catch (Exception parseException) {
                log.warn("⚠️ AI返回的JSON解析失败，使用纯AI备用方案", parseException);
                // 使用纯AI备用方案生成相似题目
                similarQuestions = generatePureAiFallbackQuestions(originalText);
            }

        } catch (Exception e) {
            log.error("❌ AI生成相似题目失败，使用纯AI备用方案", e);
            similarQuestions = generatePureAiFallbackQuestions(originalText);
        }

        return similarQuestions;
    }

    /**
     * 纯AI备用方案：生成相似题目
     */
    private List<Map<String, Object>> generatePureAiFallbackQuestions(String originalText) {
        List<Map<String, Object>> fallbackQuestions = new ArrayList<>();

        try {
            log.info("🔄 使用纯AI备用方案生成相似题目...");

            // 使用更简单的提示词重新尝试
            String simplifiedPrompt = String.format("""
                请基于题目"%s"生成3道相似题目。
                要求：
                1. 保持相同知识点
                2. 改变数值或条件
                3. 返回JSON格式：[{"question":"题目","answer":"答案","explanation":"解释"}]
                """, originalText.length() > 200 ? originalText.substring(0, 200) + "..." : originalText);

            String aiResponse = aiChatService.chat(simplifiedPrompt);

            // 尝试解析简化的AI响应
            try {
                List<Map<String, Object>> aiQuestions = safeParseJsonArray(aiResponse);

                for (int i = 0; i < Math.min(3, aiQuestions.size()); i++) {
                    Map<String, Object> aiQuestion = aiQuestions.get(i);
                    Map<String, Object> similar = new HashMap<>();

                    similar.put("id", "pure_ai_" + System.currentTimeMillis() + "_" + i);
                    similar.put("questionText", aiQuestion.get("question"));
                    similar.put("answer", aiQuestion.get("answer"));
                    similar.put("explanation", aiQuestion.get("explanation"));
                    similar.put("generatedBy", "PURE_AI_FALLBACK");
                    similar.put("generatedTime", LocalDateTime.now());

                    fallbackQuestions.add(similar);
                }

                log.info("✅ 纯AI备用方案成功生成{}道题目", fallbackQuestions.size());

            } catch (Exception parseError) {
                log.warn("⚠️ 纯AI备用方案解析失败，使用最终模板方案", parseError);
                fallbackQuestions = generateFinalTemplateFallback(originalText);
            }

        } catch (Exception e) {
            log.warn("⚠️ 纯AI备用方案失败，使用最终模板方案", e);
            fallbackQuestions = generateFinalTemplateFallback(originalText);
        }

        return fallbackQuestions;
    }

    /**
     * 最终模板备用方案
     */
    private List<Map<String, Object>> generateFinalTemplateFallback(String originalText) {
        List<Map<String, Object>> templateQuestions = new ArrayList<>();

        for (int i = 1; i <= 3; i++) {
            Map<String, Object> similar = new HashMap<>();
            similar.put("id", "template_final_" + System.currentTimeMillis() + "_" + i);
            similar.put("questionText", String.format("相似题目 %d：基于原题目的变化练习", i));
            similar.put("answer", "请根据题目要求进行分析和计算");
            similar.put("explanation", "这是一道与原题相似的练习题，请运用相同的解题方法");
            similar.put("generatedBy", "FINAL_TEMPLATE");
            similar.put("generatedTime", LocalDateTime.now());

            templateQuestions.add(similar);
        }

        log.info("📝 最终模板方案生成{}道题目", templateQuestions.size());
        return templateQuestions;
    }



    /**
     * 生成相似题目文本
     */
    private String generateSimilarQuestionText(String originalText, int index) {
        if (originalText == null) return "相似题目 " + index;

        String[] variations = {
            "类似题目：" + originalText.replace("求", "计算"),
            "相关练习：" + originalText.replace("已知", "设"),
            "举一反三：" + originalText.replace("的值", "的结果")
        };

        return variations[(index - 1) % variations.length];
    }

    // ==================== 统一响应构建方法 ====================

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(String message, String errorCode) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("errorCode", errorCode);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 构建分页响应
     */
    private Map<String, Object> buildPageResponse(Object data, int page, int size, long total) {
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("page", page);
        pagination.put("size", size);
        pagination.put("total", total);
        pagination.put("totalPages", (total + size - 1) / size);

        Map<String, Object> responseData = new HashMap<>();
        responseData.put("list", data);
        responseData.put("pagination", pagination);

        return buildSuccessResponse("查询成功", responseData);
    }
}
