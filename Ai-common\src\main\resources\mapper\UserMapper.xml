<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhentao.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="cn.zhentao.pojo.User">
            <id property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="userType" column="user_type" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="phonenumber" column="phonenumber" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="CHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="loginDate" column="login_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,user_name,nick_name,
        user_type,email,phonenumber,
        sex,avatar,password,
        status,del_flag,login_date,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
