package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜题记录实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "search_records", autoResultMap = true)
public class SearchRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话名称
     */
    private String sessionName;

    /**
     * 题目图片URL
     */
    private String imageUrl;

    /**
     * 图片哈希值
     */
    private String imageHash;

    /**
     * OCR识别的文字内容
     */
    private String ocrText;

    /**
     * OCR识别的LaTeX公式
     */
    private String ocrLatex;

    /**
     * 学科
     */
    private String subject;

    /**
     * 题型
     */
    private String questionType;

    /**
     * 知识点列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> knowledgePoints;

    /**
     * 难度等级(1-5)
     */
    private Integer difficultyLevel;

    /**
     * AI分析过程
     */
    private String aiAnalysis;

    /**
     * 解题步骤
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> solutionSteps;

    /**
     * 最终答案
     */
    private String finalAnswer;

    /**
     * 置信度分数
     */
    private BigDecimal confidenceScore;

    /**
     * 处理时间(毫秒)
     */
    private Integer processingTime;

    /**
     * 使用的模型版本
     */
    private String modelVersion;

    /**
     * 是否为错题
     */
    private Boolean isWrong;

    /**
     * 掌握状态(0-未掌握,1-部分掌握,2-已掌握)
     */
    private Integer masteryStatus;

    /**
     * 复习次数
     */
    private Integer reviewCount;

    /**
     * 最后复习时间
     */
    private LocalDateTime lastReviewTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
