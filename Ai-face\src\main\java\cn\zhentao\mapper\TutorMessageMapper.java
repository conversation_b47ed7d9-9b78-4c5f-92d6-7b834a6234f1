package cn.zhentao.mapper;

import cn.zhentao.pojo.TutorMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI搜题对话消息Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface TutorMessageMapper extends BaseMapper<TutorMessage> {
    
    /**
     * 根据会话ID获取消息列表（分页）
     */
    @Select("SELECT * FROM tutor_messages WHERE session_id = #{sessionId} AND is_deleted = 0 " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{size}")
    List<TutorMessage> getMessagesBySessionId(@Param("sessionId") Long sessionId, 
                                            @Param("offset") Integer offset, 
                                            @Param("size") Integer size);
    
    /**
     * 根据会话ID获取完整对话历史
     */
    @Select("SELECT * FROM tutor_messages WHERE session_id = #{sessionId} AND is_deleted = 0 " +
            "ORDER BY created_time ASC")
    List<TutorMessage> getFullConversationBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 获取会话的最后一条消息
     */
    @Select("SELECT * FROM tutor_messages WHERE session_id = #{sessionId} AND is_deleted = 0 " +
            "ORDER BY created_time DESC LIMIT 1")
    TutorMessage getLastMessageBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 统计会话消息数量
     */
    @Select("SELECT COUNT(*) FROM tutor_messages WHERE session_id = #{sessionId} AND is_deleted = 0")
    Integer countMessagesBySessionId(@Param("sessionId") Long sessionId);
}
