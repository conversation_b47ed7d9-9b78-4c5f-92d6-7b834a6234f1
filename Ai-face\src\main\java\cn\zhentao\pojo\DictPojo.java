package cn.zhentao.pojo;

public class DictPojo {
    public String getClass_num() {
        return class_num;
    }

    public void setClass_num(String class_num) {
        this.class_num = class_num;
    }

    public String getSort_batch() {
        return sort_batch;
    }

    public void setSort_batch(String sort_batch) {
        this.sort_batch = sort_batch;
    }

    public String getPims_breed() {
        return pims_breed;
    }

    public void setPims_breed(String pims_breed) {
        this.pims_breed = pims_breed;
    }

    public String getSort_finished_class() {
        return sort_finished_class;
    }

    public void setSort_finished_class(String sort_finished_class) {
        this.sort_finished_class = sort_finished_class;
    }

    public String getPims_umbrella_shape() {
        return pims_umbrella_shape;
    }

    public void setPims_umbrella_shape(String pims_umbrella_shape) {
        this.pims_umbrella_shape = pims_umbrella_shape;
    }

    public String getProject_type() {
        return project_type;
    }

    public void setProject_type(String project_type) {
        this.project_type = project_type;
    }

    public String getProduct_place() {
        return product_place;
    }

    public void setProduct_place(String product_place) {
        this.product_place = product_place;
    }

    String  class_num;
  String  sort_batch;
  String  pims_breed;
  String  sort_finished_class;
  String  pims_umbrella_shape;
  String  project_type;
  String  product_place;
}
