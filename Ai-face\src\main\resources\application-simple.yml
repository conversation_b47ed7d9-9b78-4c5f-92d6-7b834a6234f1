server:
  port: 8081

spring:
  # 暂时禁用数据库自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
  main:
    allow-circular-references: true

# AI服务配置（使用通义千问兼容接口）
ai:
  api-key: sk-8a4bac4735da4f79b19383d2b2696eac
  base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
  model-name: qwen-vl-plus

# 日志配置
logging:
  level:
    root: info
    cn.zhentao: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
