export default {
  login: {
    tokenExpired: 'Login token expired, please log in again!',
    uid: {
      title: 'Please confirm your UID',
      holder: 'Enter UID here, minimum 8 digits, maximum 16 digits',
    },
    btn: 'Confirm',
    logging: 'Logging in...',
    failed: 'Login Failed',

    agreement: {
      prefix: 'I have read and agree to the ',
      suffix: ' in detail',
      userAggreement: 'User Agreement',
      privacyPolicy: 'Privacy Policy',
      and: ' and ',
      required: 'Please read and agree to the Privacy Policy first',
    },

    phone: {
      title: 'Login with Phone Number',
      holder: 'Please enter your phone number',
      errorMessage: 'Please enter a valid phone number',

      verification: {
        title: 'Get SMS Verification Code',
        btn: 'Get',
        getting: 'Getting Verification Code...',
        holder: 'Enter Verification Code',
        success: 'Verification code has been sent to {phone}',
        resend: 'Re-get Verification Code',
        wait: '{count} seconds',
        failed: 'Failed to get SMS verification code.',
        errorMessage: 'Verification Code has error',
        exceeded: 'The limit for obtaining SMS verification code has been exceeded, please try again after 1 hour',
      },
      captcha: {
        holder: 'Please enter the image verification code',
        failed: 'Failed to get the image verification code',
        refresh: 'Click to switch',
        errorMessage: 'Verification code has error',
      },
    },
  },

  logout: {
    text: 'Logout',
  },

  welcome: {
    title: 'AI Agent',
    btn: 'Start',
    call: 'Agent Call',
    pstn: 'AI Phone Call',

    optionsTitle: 'Options',
    options: {
      emotion: {
        title: 'Emotion Support',
        help: 'Does agent support emotional label output?',
        options: {
          unemotional: 'Unemotional',
          emotional: 'Emotional',
        },
      },
    },
  },

  system: {
    notSupported: 'Your browser does not support WebRTC. Please use DingTalk or WeChat to access this page.',
    generateByAI: 'Content generated by AI, for reference only',
    connecting: 'Connecting...',
  },

  agent: {
    voice: 'AI Voice Call',
    avatar: 'AI Avatar Call',
    vision: 'AI Vision Call',
    chatbot: 'AI Chatbot',
    video: 'AI Video Call',

    ended: 'Call ended',
    endedByInactivity: 'Due to your prolonged inactivity, the call has ended.',
    endedByAgent: 'The call has ended.',

    receivedCustomMessage: 'Received custom message:{msg}',
    visionCustomCaptureState: 'Custom vision capture state:{enabled}',
    interrupted: 'Speaking interrupted: {reason}',

    voiceprintIgnored: 'Detected other speaker, stop responded this question.',
    aivadIgnored: 'VAD detected other speaker, stop responded this question.',
  },

  hero: {
    name: 'XiaoYun',
  },

  actions: {
    clickToCall: 'Click to Call',
    call: 'Call',
    handup: 'Handup',
  },

  resume: {
    title: 'Audio/Video playback failed',
    content: 'Please click the "Confirm" button to resume playback',
    btn: 'Confirm',
  },

  subtitleList: {
    btn: 'Subtitle',
  },

  settings: {
    title: 'Settings',
    mode: {
      title: 'Mode',
      pushToTalk: 'Push to Talk Mode',
      natural: 'Natural Conversation Mode',
    },

    failed: 'Failed to change settings',
    interrupt: {
      title: 'Intelligent Interrupt',
      help: 'Interrupt AI based on sound and environment',
      enabled: 'Intelligent interruption is turned on',
      disabled: 'Intelligent interruption is turned off',
    },
    voiceId: {
      title: 'Choose Voice Tone',
      help: 'New Voice Tone Will Take Effect in Next Response',
      success: 'Voice Tone changed successfully',
    },

    pushToTalk: {
      failed: 'Failed to enable/disable push to talk Mode',
      enabled: 'Push to talk mode is turned on',
      disabled: 'Push to talk mode is turned off',
      spaceTip:
        'Push To Talk Mode is turned on, press space to start talking, and the microphone is enabled by default in push to talk mode.',
    },
  },

  pushToTalk: {
    push: 'Push to Talk',
    releaseToSend: 'Release to Send',
    tip: 'Push to Talk, Release to Send',
    spaceTip: 'Press space to start talking',
    tooShort: 'Canceled due to short hold time.',
    canceled: 'You have canceled to send your talk.',
  },

  microphone: {
    open: 'Open',
    close: 'Close',
    closed: 'Closed',
    opened: 'Opened',
  },

  camera: {
    switch: 'Switch',
    open: 'Open',
    close: 'Close',
    closed: 'Closed',
    opened: 'Opened',
  },

  status: {
    listeningToStart: "I'm Listening...",
    listening: "You Talk, I'm Listening...",
    thinking: 'Thinking...',
    speaking: "I'm Replying, Press TAB or Speak to Interrupt Me",
    speakingNoInterrupt: "I'm Replying, Press TAB to Interrupt Me",
    interrupted: 'Speaking interrupted',

    mobile: {
      speaking: "I'm Replying, Tap Screen or Speak to Interrupt Me",
      speakingNoInterrupt: "I'm Replying, Tap Screen to Interrupt Me",
    },
  },

  error: {
    localDeviceException: 'Call Failed, Local Device Error',
    tokenExpired: 'Call Failed, Authorization Expired',
    connectionFailed: 'Call Failed, Network Connection Issue',
    kickedByUserReplace: 'Call Failed, User May Be Logged In on Another Device',
    kickedBySystem: 'Call Failed, Ended by System',
    agentLeaveChannel: 'Call Failed, Agent Stopped',
    agentPullFailed: 'Call Failed, Agent Failed to Pull Stream',
    agentASRFailed: 'Call Failed, The Third Party Service of ASR Failed to Start',
    avatarServiceFailed: 'Call Failed, Avatar Agent Service Failed to Start',
    avatarRoutesExhausted:
      'AI avatar calling is in high demand, please try again later or enjoy the new experience of AI voice calling first.',
    subscriptionRequired: 'Call Failed, Subscription Required',
    agentNotFound: 'Call Failed, Agent Not Found',
    unknown: 'Call Failed, Unknown Error',
  },

  avatar: {
    timeLimit: 'The call has ended. The avatar agent call can only be experienced for 5 minutes.',
  },

  vision: {
    customCapture: {
      enabled: 'Custom frame capture inspection mode has been enabled.',
      disabled: 'Exited custom frame capture inspection mode.',
    },
  },

  humanTakeover: {
    willStart: 'The current call will soon be handled by a real person.',
    connected: 'The current call is now being handled by a real person.',
  },

  share: {
    tokenExpired: 'Invalid Token',
    tokenInvalid: 'Token Expired',
  },

  issue: {
    title: 'Report Issues',
    type: 'Issues',
    requiredTip: 'Please select the issue type.',
    submit: 'Submit',
    options: {
      multipleSelected: '(Multiple Selected)',
      notAvaliable: 'Feature not available',
      hasBugs: 'Available but has bugs',
      tooSlow: 'AI response is too slow',
      notAccurate: 'AI response content is not accurate',
      quality: 'Audio/Video quality issues',
      other: 'Other issues',
      description: 'Please describe your issue.',
      descriptionMax: 'Description can not exceed 100 characters.',
    },
    success: {
      title: 'Successfully submitted.',
      message: 'Thank you for your support. Your request ID is {reqId}',
    },
  },
  common: {
    cancel: 'Cancel',
    confirm: 'Confirm',
    ok: 'Ok',
    copy: 'Copy',
    copySuccess: 'Copied',
    copyFailed: 'Copy failed',
    use: 'Use',
    close: 'Close',
    exit: 'Exit',
    back: 'Back',
    delete: 'Delete',
  },

  chat: {
    connecting: 'Connecting...',
    disconnected: 'Disconnected',

    history: {
      failed: 'Failed to retrieve historical messages',
      noMore: 'No more messages',

      pullingText: 'Pull down to load more',
      canReleaseText: 'Release to load',
      completeText: 'Loaded',
      refreshingText: 'Loading...',
    },

    message: {
      copied: 'Message copied',
      deleteConfirm: 'Confirm delete message?',
      deleteHelp: 'Deleted messages cannot be restored.',
      deleteFailed: 'Delete failed',

      customReceived: 'Received custom message: {msg}',

      tableTitle: 'Table',
    },

    actions: {
      album: 'Album',
      toVoice: 'Voice Call',
      toAvatar: 'Avatar Call',
      toVision: 'Vision Call',
      toVideo: 'Video Call',
    },

    send: {
      textHolder: 'Please enter content',
      voice: {
        tip: 'Press and hold to speak',
        releaseToSend: 'Release to send, swipe up to cancel',
        releaseToCancel: 'Release to cancel',
        failed: 'Failed to send voice message',
        noText: 'No text recognized',
        noPermission: 'Failed to start microphone, please check device and permission',
        tooShort: 'Canceled due to short hold time.',
      },
    },

    uploader: {
      imageFailed: 'Failed to upload image',
      notReady: 'Some images uploading or failed to upload',

      countLimit: 'Max {count} files can be uploaded at once ',
      sizeLimit: 'File can not exceed {size}',
      noSVG: 'SVG File  is not supported',
    },

    response: {
      interrupted: 'The user terminated this response',
      reasoninging: 'Reasoning...',
      reasoningCompleted: 'Reasoning completed',
      reasoningInterrupted: 'Reasoning stopped',
    },

    playback: {
      failed: 'Playback failed',
      generating: 'Generating speech reading...',
    },
  },

  pstn: {
    title: 'AI OutboundCall',
    phone: {
      required: 'Phone number is required',
      error: 'Phone number is not valid',
      label: 'Calling Number',
      placeholder: 'Input your phone number',
      tip: 'Only mainland China phone numbers are supported.',
    },
    interrupt: {
      label: 'Smart Interrupt',
    },
    voiceId: {
      label: 'Choose Voice Tone',
    },
    help: 'The system will place an AI call to the recipient after you proceed. Please be ready to receive it.',
    start: 'Start',
    result: {
      fail: 'Call failed，Code: {code}',
      success: 'The call is being placed, please be ready to answer',
      copy: 'Copy',
    },
  },
};
