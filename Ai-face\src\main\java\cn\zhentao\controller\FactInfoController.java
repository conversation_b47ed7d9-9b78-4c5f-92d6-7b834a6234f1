package cn.zhentao.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import cn.zhentao.pojo.User;
import cn.zhentao.service.MinioService;
import cn.zhentao.service.impl.FactInfoServiceImpl;
import cn.zhentao.utils.ArcFaceEngineUtil;
import cn.zhentao.utils.ImageQualityChecker;
import cn.zhentao.utils.RedisUtil;
import com.arcsoft.face.FaceFeature;
import com.arcsoft.face.FaceSimilar;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 人脸信息管理控制器
 *
 * 提供人脸识别相关的REST API接口，包括：
 * 1. 人脸注册 - 将用户人脸信息存储到数据库和MinIO
 * 2. 人脸比对 - 比较两张人脸图片的相似度
 * 3. 人脸识别登录 - 通过人脸识别进行用户身份验证
 * 4. 人脸信息管理 - 查询、删除人脸信息
 *
 * 使用虹软人脸识别SDK进行人脸特征提取和比对
 * 使用MinIO进行图片文件存储
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Slf4j
@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/fact-info")
@SuppressWarnings("unchecked")
public class FactInfoController {

    /**
     * 文件访问基础URL，从配置文件中读取
     */
    @Value("${file.url}")
    public String url;

    /**
     * 人脸信息服务实现类，用于数据库操作
     */
    @Resource
    FactInfoServiceImpl factInfoService;

    /**
     * 虹软人脸识别引擎工具类，用于人脸特征提取和比对
     */
    @Resource
    ArcFaceEngineUtil arcFaceEngineUtil;

//    /**
//     * 人脸信息日志服务，用于记录人脸识别操作日志
//     */
//    @Resource
//    FactInfoLogService factInfoLogService;

    /**
     * MinIO文件存储服务，用于图片文件的上传和管理
     */
    @Resource
    MinioService minioService;

    /**
     * 图片质量检查工具，用于验证图片是否适合人脸识别
     */
    @Resource
    ImageQualityChecker imageQualityChecker;

    /**
     * 人脸注册接口
     *
     * 接收用户上传的人脸图片，提取人脸特征并存储到数据库
     * 同时将图片文件上传到MinIO对象存储服务器
     *
     * @param files 用户上传的人脸图片文件数组（支持多张图片，但只处理第一张）
     * @param name 用户姓名
     * @param remark 备注信息
     * @return ResponseEntity 包含注册结果的响应实体
     *         - code: 状态码（200成功，500失败）
     *         - message: 操作结果消息
     *         - data: 注册成功的用户信息
     * @throws IOException 文件操作异常
     */
    @PostMapping("/save")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveFaceInfo(@RequestParam MultipartFile[] files, String name, String remark) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        File tempFile = null;

        try {
            // 参数验证
            if (files == null || files.length == 0) {
                map.put("code", 400);
                map.put("message", "请上传人脸图片");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            if (name == null || name.trim().isEmpty()) {
                map.put("code", 400);
                map.put("message", "请输入用户姓名");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            log.info("开始注册用户人脸信息，用户名: {}, 图片数量: {}", name, files.length);

            // 只处理第一张图片
            MultipartFile faceFile = files[0];

            // 创建临时文件用于人脸特征提取
            tempFile = File.createTempFile("register_face_", ".jpg");
            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(faceFile.getBytes());
            }

            // 提取人脸特征
            FaceFeature faceFeature = arcFaceEngineUtil.getFaceFeature(tempFile);
            if (faceFeature == null) {
                map.put("code", 501);
                map.put("message", "未检测到人脸信息，请上传清晰的人脸照片");
                return new ResponseEntity<>(map, HttpStatus.OK);
            }

            // 将图片上传到MinIO
            String fileName = null;
            try {
                fileName = minioService.uploadFile(faceFile, null);
                log.info("图片上传到MinIO成功，文件名: {}", fileName);
            } catch (Exception e) {
                log.error("图片上传到MinIO失败", e);
                map.put("code", 500);
                map.put("message", "图片上传失败：" + e.getMessage());
                return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            // 构建用户人脸信息对象
            User user = new User();
            user.setUsername(name.trim());
            user.setRemark(remark != null ? remark.trim() : "");

            // 将人脸特征转换为JSON字符串存储
            String jsonStr = JSONUtil.toJsonStr(faceFeature);
            user.setFaceData(jsonStr);

            // 设置图片文件名（用于后续访问）
            user.setImageUrl(fileName);

            // 保存到数据库
            HashMap<String, Object> saveResult = factInfoService.saveFaceInfo(user);
            saveResult.put("data", user);
            saveResult.put("imageUrl", url + fileName);

            log.info("用户人脸信息注册成功，用户名: {}, ID: {}", name, user.getId());
            return new ResponseEntity<>(saveResult, HttpStatus.OK);

        } catch (Exception e) {
            log.error("人脸注册过程中发生异常，用户名: {}", name, e);
            map.put("code", 500);
            map.put("message", "注册失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 删除人脸信息接口
     *
     * 根据用户ID删除数据库中的人脸信息记录
     * 同时删除MinIO中对应的图片文件
     *
     * @param resout 请求体，包含要删除的用户ID
     * @return ResponseEntity 包含删除结果的响应实体
     *         - code: 状态码（200成功，500失败）
     *         - message: 操作结果消息
     */
    @PostMapping("/delete")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteFaceInfo(@RequestBody Map<String, Object> resout) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            // 参数验证
            if (resout.get("id") == null) {
                map.put("code", 400);
                map.put("message", "请提供要删除的用户ID");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            String userId = resout.get("id").toString();
            log.info("开始物理删除人脸信息，用户ID: {}", userId);
            // 先查询用户信息，获取图片文件名
            User user = factInfoService.getById(userId);
            if (user != null && user.getImageUrl() != null) {
                try {
                    // 删除MinIO中的图片文件
                    minioService.deleteFile(user.getImageUrl());
                    log.info("MinIO中的图片文件删除成功: {}", user.getImageUrl());
                } catch (Exception e) {
                    log.warn("删除MinIO中的图片文件失败: {}", user.getImageUrl(), e);
                }
            }
            // 物理删除数据库记录
            boolean removed = factInfoService.physicalDeleteById(userId);
            if (removed) {
                map.put("code", 200);
                map.put("message", "物理删除成功");
                log.info("用户人脸信息物理删除成功 - 用户ID: {}", userId);
            } else {
                map.put("code", 500);
                map.put("message", "数据库物理删除失败");
                log.warn("用户人脸信息物理删除失败 - 用户ID: {}", userId);
            }
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除人脸信息时发生异常", e);
            map.put("code", 500);
            map.put("message", "删除失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 人脸识别登录接口
     *
     * 接收前端传来的Base64编码的人脸图片，进行人脸识别验证
     * 与数据库中已注册的人脸信息进行比对，判断是否为合法用户
     * 同时记录登录日志和人脸分析信息（年龄、性别、活体检测等）
     *
     * @param resout 请求体，包含Base64编码的图片数据
     * @return ResponseEntity 包含识别结果的响应实体
     *         - code: 状态码（200登录成功，500非法用户，501无人脸信息）
     *         - message: 操作结果消息
     *         - data: 人脸分析信息（年龄、性别等）
     *         - userInfo: 匹配成功的用户信息
     * @throws IOException 文件操作异常
     */
    @PostMapping("/check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkFaceInfo(@RequestBody Map<String, Object> resout) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        File tempFile = null;
        String uploadedFileName = null;
        String imageId = null;
        try {
            // 参数验证
            if (resout.get("imageData") == null) {
                map.put("code", 400);
                map.put("message", "请提供图片数据");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            log.info("开始人脸识别登录验证");
            // 解析Base64图片数据
            String imageDataStr = resout.get("imageData").toString();
            log.info("接收到的图片数据长度: {}", imageDataStr.length());
            String[] imageData = imageDataStr.split(",");
            if (imageData.length < 2) {
                map.put("code", 400);
                map.put("message", "图片数据格式错误，请确保是完整的Base64格式");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            // 检查Base64头部信息
            String header = imageData[0];
            log.info("图片格式头部: {}", header);
            // Base64解码
            byte[] result;
            try {
                result = Base64.decode(imageData[1]);
                log.info("Base64解码成功，图片大小: {}KB", result.length / 1024);
            } catch (Exception e) {
                log.error("Base64解码失败", e);
                map.put("code", 400);
                map.put("message", "Base64解码失败，请检查图片数据格式");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            // 存入Redis，24小时过期
            imageId = UUID.randomUUID().toString();
            String redisKey = "login:face:" + imageId;
            HashMap<String, Object> redisValue = new HashMap<>();
            redisValue.put("imageId", imageId);
            redisValue.put("imageData", imageDataStr);
            String redisJson = JSONUtil.toJsonStr(redisValue);
            RedisUtil.setString(redisKey, redisJson, 86400); // 86400秒=24小时
            log.info("登录图片Base64及ID已存入Redis，key: {}，过期时间24小时", redisKey);
            // 调整字节数据
            for (int i = 0; i < result.length; ++i) {
                if (result[i] < 0) {
                    result[i] += 256;
                }
            }
            // 检查图片质量
            ImageQualityChecker.ImageQualityResult qualityResult = imageQualityChecker.checkImageQuality(result);
            log.info("图片质量检查结果: {}", qualityResult);
            if (!qualityResult.isValid()) {
                map.put("code", 400);
                map.put("message", "图片质量不符合要求: " + String.join(", ", qualityResult.getErrors()));
                map.put("qualityCheck", qualityResult);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            if (!qualityResult.getWarnings().isEmpty()) {
                log.warn("图片质量警告: {}", qualityResult.getWarnings());
            }
            // 上传图片到MinIO
            try {
                uploadedFileName = minioService.uploadFile(result, imageId + ".jpg", "image/jpeg");
                log.info("登录图片上传到MinIO成功，文件名: {}", uploadedFileName);
            } catch (Exception e) {
                log.error("登录图片上传到MinIO失败", e);
                map.put("code", 500);
                map.put("message", "图片处理失败");
                return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            // 创建临时文件用于人脸识别
            tempFile = File.createTempFile("login_face_", ".jpg");
            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(result);
            }
            // 提取人脸特征
            FaceFeature faceFeature = arcFaceEngineUtil.getFaceFeature(tempFile);
            // 检查是否检测到人脸
            if (faceFeature == null) {
                map.put("code", 501);
                map.put("message", "未检测到人脸信息，请确保人脸清晰可见");
                map.put("data", null);
                return new ResponseEntity<>(map, HttpStatus.OK);
            }
            // 获取人脸详细信息（年龄、性别、活体检测等）
            HashMap<String, Object> userInfo = arcFaceEngineUtil.getUserInfo(tempFile);
            // 与数据库中所有注册用户进行人脸比对
            List<User> registeredUsers = factInfoService.list();
            log.info("开始与 {} 个注册用户进行人脸比对", registeredUsers.size());
            for (User registeredUser : registeredUsers) {
                FaceFeature registeredFaceFeature = JSONUtil.toBean(registeredUser.getFaceData(), FaceFeature.class);
                FaceSimilar faceSimilar = arcFaceEngineUtil.compareFaceFeature(faceFeature, registeredFaceFeature);
                double similarity = faceSimilar.getScore();
                log.debug("与用户 {} 的相似度: {}", registeredUser.getUsername(), similarity);
                if (similarity > 0.78) {
                    // 生成会话ID
                    String sessionId = UUID.randomUUID().toString();
                    String loginTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                    // 构建登录用户信息
                    Map<String, Object> loginUserInfo = new HashMap<>();
                    loginUserInfo.put("userId", registeredUser.getId());
                    loginUserInfo.put("username", registeredUser.getUsername());
                    loginUserInfo.put("nickname", registeredUser.getNickname());
                    loginUserInfo.put("email", registeredUser.getEmail());
                    loginUserInfo.put("phonenumber", registeredUser.getPhonenumber());
                    loginUserInfo.put("sex", registeredUser.getSex());
                    loginUserInfo.put("avatar", registeredUser.getAvatar());
                    loginUserInfo.put("sessionId", sessionId);
                    loginUserInfo.put("loginTime", loginTime);
                    loginUserInfo.put("loginType", "FACE_RECOGNITION");
                    loginUserInfo.put("similarity", similarity);
                    loginUserInfo.put("faceAnalysis", userInfo);

                    // 将登录信息存储到Redis，24小时过期
                    String userSessionKey = "user:session:" + sessionId;
                    String userLoginKey = "user:login:" + registeredUser.getId();

                    // 存储会话信息
                    RedisUtil.setString(userSessionKey, JSONUtil.toJsonStr(loginUserInfo), 86400);
                    // 存储用户最新登录信息
                    RedisUtil.setString(userLoginKey, JSONUtil.toJsonStr(loginUserInfo), 86400);

                    log.info("用户 {} 登录成功，会话ID: {}，相似度: {}", registeredUser.getUsername(), sessionId, similarity);

                    // 更新用户最后登录时间
                    registeredUser.setLoginDate(new Date());
                    registeredUser.setUpdateTime(new Date());
                    factInfoService.updateById(registeredUser);

                    map.put("code", 200);
                    map.put("data", userInfo);
                    map.put("message", "恭喜 " + registeredUser.getUsername() + " 登录成功！");
                    map.put("userInfo", loginUserInfo);
                    map.put("sessionId", sessionId);
                    map.put("similarity", similarity);
                    return new ResponseEntity<>(map, HttpStatus.OK);
                }
            }
            // 未找到匹配的用户
            map.put("code", 500);
            map.put("message", "人脸识别失败，未找到匹配的用户信息");
            map.put("data", userInfo);
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (Exception e) {
            log.error("人脸识别登录过程中发生异常", e);
            map.put("code", 500);
            map.put("message", "登录验证失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 验证会话并获取用户信息
     *
     * @param sessionId 会话ID
     * @return ResponseEntity 包含用户信息的响应实体
     */
    @GetMapping("/session/{sessionId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getUserBySession(@PathVariable String sessionId) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            String userSessionKey = "user:session:" + sessionId;
            String userInfoJson = RedisUtil.getString(userSessionKey);

            if (userInfoJson == null) {
                map.put("code", 401);
                map.put("message", "会话已过期或无效");
                return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
            }

            Map<String, Object> userInfo = JSONUtil.toBean(userInfoJson, Map.class);
            map.put("code", 200);
            map.put("message", "获取用户信息成功");
            map.put("data", userInfo);

            return new ResponseEntity<>(map, HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取用户会话信息时发生异常", e);
            map.put("code", 500);
            map.put("message", "获取用户信息失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 用户登出
     *
     * @param sessionId 会话ID
     * @return ResponseEntity 包含登出结果的响应实体
     */
    @PostMapping("/logout/{sessionId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> logout(@PathVariable String sessionId) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            String userSessionKey = "user:session:" + sessionId;

            // 获取用户信息
            String userInfoJson = RedisUtil.getString(userSessionKey);
            if (userInfoJson != null) {
                Map<String, Object> userInfo = JSONUtil.toBean(userInfoJson, Map.class);
                String userId = userInfo.get("userId").toString();
                String userLoginKey = "user:login:" + userId;

                // 删除会话和登录信息
                RedisUtil.del(userSessionKey);
                RedisUtil.del(userLoginKey);

                log.info("用户 {} 登出成功，会话ID: {}", userInfo.get("username"), sessionId);
            }

            map.put("code", 200);
            map.put("message", "登出成功");
            return new ResponseEntity<>(map, HttpStatus.OK);

        } catch (Exception e) {
            log.error("用户登出时发生异常", e);
            map.put("code", 500);
            map.put("message", "登出失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}


