server:
  port: 8081

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************
    username: root
    password: Sunshuo0818
  main:
    allow-circular-references: true
redis:
  host: **************
  port: 6379
  password: 2308


# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

file:
  url: http://**************:19000/test/

# MinIO配置
minio:
  endpoint: http://**************:19000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: test

#  人脸识别
arcface:
  appId: 4a283TA94rAADDaFHK1bMt9SpHvSSUrDT72UHq57ftFD
  sdkKey: aNcQAxi9yKFQL6yFzzPerUHmi96roviGRQGragDwLEp
  #  dllPath: D:\tool\face\libs\WIN64
#  dllPath: E:\wwwroot\libs\WIN64


# LangChain4j配置
langchain4j:
  dashscope:
    api-key: sk-8a4bac4735da4f79b19383d2b2696eac
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    model-name: qwen-vl-plus

# AI搜题配置
ai-tutor:
  # 图片存储配置
  image:
    upload-path: /uploads/questions/
    max-size: 10MB
    allowed-types: jpg,jpeg,png,bmp,gif
  # 缓存配置
  cache:
    enabled: true
    expire-time: 7d
    max-size: 1000
  # 举一反三配置
  similar-questions:
    count: 3
    similarity-threshold: 0.8

# 阿里云OCR配置
aliyun:
  ocr:
    access-key-id: LTAI5tLMfTkbjvcvNbsPAjCL
    access-key-secret: ******************************
    region-id: cn-shanghai
    # OCR API接入点，默认使用杭州节点
    endpoint: ocr-api.cn-hangzhou.aliyuncs.com

# AI服务配置（使用通义千问兼容接口）
ai:
  api-key: sk-8a4bac4735da4f79b19383d2b2696eac
  base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
  model-name: qwen-vl-plus