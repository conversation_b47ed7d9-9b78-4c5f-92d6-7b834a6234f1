package cn.zhentao.service.impl;

import cn.zhentao.mapper.FactInfoMapper;
import cn.zhentao.pojo.User;
import cn.zhentao.service.FactInfoService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Slf4j
@Service
public class FactInfoServiceImpl extends ServiceImpl<FactInfoMapper, User> implements FactInfoService {
    @Resource
    FactInfoMapper factInfoMapper;
    @Resource
    FactInfoService factInfoService;

    @Override
    public HashMap<String, Object> saveFaceInfo(User user) {
        int insert = factInfoMapper.insert(user);
        HashMap<String, Object> map = new HashMap<>();
        if (insert > 0) {
            map.put("code", 200);
            map.put("message", "存储用户人脸信息成功");
        }
        return map;
    }

    /**
     * 物理删除用户人脸信息
     *
     * @param userId 用户ID
     * @return 是否删除成功
     */
    public boolean physicalDeleteById(String userId) {
        try {
            log.info("执行物理删除操作 - 用户ID: {}", userId);

            // 直接使用SQL删除，绕过逻辑删除
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", userId);

            // 使用factInfoMapper直接删除，这会执行物理删除
            int result = factInfoMapper.delete(queryWrapper);

            log.info("物理删除结果 - 用户ID: {}, 影响行数: {}", userId, result);
            return result > 0;

        } catch (Exception e) {
            log.error("物理删除失败 - 用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 根据用户名查询用户（忽略逻辑删除）
     *
     * @param username 用户名
     * @return 用户信息
     */
    public User getByUsernameIgnoreDeleted(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        // 不添加逻辑删除条件，查询所有记录
        return factInfoMapper.selectOne(queryWrapper);
    }
}
