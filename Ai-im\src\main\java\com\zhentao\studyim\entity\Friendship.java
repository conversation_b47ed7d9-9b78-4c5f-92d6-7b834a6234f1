package com.zhentao.studyim.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 好友关系实体类
 */
@Data
@Entity
@Table(name = "friendship")
public class Friendship {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;                      // 用户ID

    @Column(name = "friend_id", nullable = false)
    private Long friendId;                    // 好友ID

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private FriendshipStatus status = FriendshipStatus.ACTIVE;  // 关系状态

    @Column(name = "create_time")
    private LocalDateTime createTime = LocalDateTime.now();  // 创建时间

    @Column(name = "update_time")
    private LocalDateTime updateTime = LocalDateTime.now();  // 更新时间

    /**
     * 好友关系状态枚举
     */
    public enum FriendshipStatus {
        ACTIVE,     // 正常
        BLOCKED,    // 已屏蔽
        DELETED     // 已删除
    }

    /**
     * 在保存前设置默认值
     */
    @PrePersist
    public void prePersist() {
        if (this.status == null) {
            this.status = FriendshipStatus.ACTIVE;
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.updateTime == null) {
            this.updateTime = LocalDateTime.now();
        }
    }

    /**
     * 在更新前设置更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
