package cn.zhentao.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * 人脸识别功能配置类
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Configuration
@ConditionalOnProperty(name = "face.recognition.enabled", havingValue = "true", matchIfMissing = false)
public class FaceRecognitionConfig {
    
    // 当face.recognition.enabled=true时，才会启用人脸识别相关功能
    
}
