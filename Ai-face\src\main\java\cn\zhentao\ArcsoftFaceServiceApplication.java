package cn.zhentao;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@MapperScan("cn.zhentao.mapper")
@ComponentScan(basePackages = {
    "cn.zhentao.controller",
    "cn.zhentao.service",
    "cn.zhentao.config",
    "cn.zhentao.utils"
})
public class ArcsoftFaceServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ArcsoftFaceServiceApplication.class, args);
        System.out.println("AI Face Recognition Service started successfully!");
    }
}