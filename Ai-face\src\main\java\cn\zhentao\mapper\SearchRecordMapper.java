package cn.zhentao.mapper;

import cn.zhentao.pojo.SearchRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 搜题记录Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface SearchRecordMapper extends BaseMapper<SearchRecord> {
    
    /**
     * 根据用户ID和错题状态查询记录
     */
    @Select("SELECT * FROM search_records WHERE user_id = #{userId} AND is_wrong = #{isWrong} " +
            "AND (#{subject} IS NULL OR subject = #{subject}) " +
            "AND (#{masteryStatus} IS NULL OR mastery_status = #{masteryStatus}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{size}")
    List<SearchRecord> selectWrongQuestions(@Param("userId") Long userId, 
                                          @Param("isWrong") Boolean isWrong,
                                          @Param("subject") String subject,
                                          @Param("masteryStatus") Integer masteryStatus,
                                          @Param("offset") Integer offset,
                                          @Param("size") Integer size);
    
    /**
     * 统计错题数量
     */
    @Select("SELECT COUNT(*) FROM search_records WHERE user_id = #{userId} AND is_wrong = #{isWrong} " +
            "AND (#{subject} IS NULL OR subject = #{subject}) " +
            "AND (#{masteryStatus} IS NULL OR mastery_status = #{masteryStatus})")
    Long countWrongQuestions(@Param("userId") Long userId, 
                           @Param("isWrong") Boolean isWrong,
                           @Param("subject") String subject,
                           @Param("masteryStatus") Integer masteryStatus);
    
    /**
     * 更新错题状态
     */
    @Update("UPDATE search_records SET is_wrong = #{isWrong}, updated_time = NOW() " +
            "WHERE id = #{recordId} AND (#{userId} IS NULL OR user_id = #{userId})")
    int updateWrongStatus(@Param("recordId") Long recordId,
                         @Param("userId") Long userId,
                         @Param("isWrong") Boolean isWrong);
    
    /**
     * 更新掌握状态
     */
    @Update("UPDATE search_records SET mastery_status = #{masteryStatus}, " +
            "review_count = review_count + 1, last_review_time = NOW(), updated_time = NOW() " +
            "WHERE id = #{recordId}")
    int updateMasteryStatus(@Param("recordId") Long recordId, 
                           @Param("masteryStatus") Integer masteryStatus);
    
    /**
     * 根据图片哈希查询缓存记录
     */
    @Select("SELECT * FROM search_records WHERE image_hash = #{imageHash} ORDER BY created_time DESC LIMIT 1")
    SearchRecord selectByImageHash(@Param("imageHash") String imageHash);
    
    /**
     * 根据知识点和题型查询相似题目
     */
    @Select("SELECT * FROM search_records WHERE " +
            "(JSON_CONTAINS(knowledge_points, JSON_ARRAY(#{knowledgePoint})) OR subject = #{subject}) " +
            "AND question_type = #{questionType} AND id != #{excludeId} " +
            "ORDER BY confidence_score DESC LIMIT #{limit}")
    List<SearchRecord> selectSimilarQuestions(@Param("knowledgePoint") String knowledgePoint,
                                            @Param("subject") String subject,
                                            @Param("questionType") String questionType,
                                            @Param("excludeId") Long excludeId,
                                            @Param("limit") Integer limit);

    /**
     * 查询会话历史
     */
    @Select("SELECT * FROM search_records WHERE user_id = #{userId} " +
            "AND (#{sessionName} IS NULL OR session_name = #{sessionName}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{size}")
    List<SearchRecord> selectSessionHistory(@Param("userId") Long userId,
                                          @Param("sessionName") String sessionName,
                                          @Param("offset") Integer offset,
                                          @Param("size") Integer size);

    /**
     * 统计会话历史数量
     */
    @Select("SELECT COUNT(*) FROM search_records WHERE user_id = #{userId} " +
            "AND (#{sessionName} IS NULL OR session_name = #{sessionName})")
    Long countSessionHistory(@Param("userId") Long userId,
                           @Param("sessionName") String sessionName);
}
