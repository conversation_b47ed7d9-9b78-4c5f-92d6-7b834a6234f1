package cn.zhentao.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@RestController
@RequestMapping("/health")
public class HealthController {

    /**
     * 健康检查接口
     */
    @GetMapping
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "ai-userlogin");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "AI User Login Service is running successfully!");
        return result;
    }

    /**
     * 版本信息
     */
    @GetMapping("/version")
    public Map<String, Object> version() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "ai-userlogin");
        result.put("version", "1.0-SNAPSHOT");
        result.put("description", "AI Tutor User Login Service");
        result.put("timestamp", LocalDateTime.now());
        return result;
    }
}
