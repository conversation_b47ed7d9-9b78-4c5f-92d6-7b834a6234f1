<template>
  <view id="app">
    <!-- 应用入口 -->
  </view>
</template>

<script>
export default {
  onLaunch: function() {
    console.log('App Launch')
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  }
}
</script>

<style>
/*每个页面公共css */
page {
  background-color: #f5f5f5;
}

/* 自定义tabBar样式 */
.uni-tabbar {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.98) 100%) !important;
  backdrop-filter: blur(30rpx) !important;
  border-top: 1rpx solid rgba(102, 126, 234, 0.1) !important;
  box-shadow: 0 -8rpx 40rpx rgba(102, 126, 234, 0.15) !important;
  height: 70px !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
  position: relative !important;
}

.uni-tabbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

.uni-tabbar__item {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  position: relative !important;
  border-radius: 20rpx !important;
  margin: 8rpx !important;
}

.uni-tabbar__item:active {
  transform: scale(0.92) !important;
}

.uni-tabbar__icon {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  position: relative !important;
}

.uni-tabbar__label {
  font-weight: 500 !important;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  font-size: 22rpx !important;
  margin-top: 4rpx !important;
}

/* 选中状态的特殊效果 */
.uni-tabbar__item.uni-tabbar__item--selected {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%) !important;
  transform: translateY(-2rpx) !important;
}

.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__icon {
  transform: scale(1.2) !important;
  filter: drop-shadow(0 4rpx 12rpx rgba(102, 126, 234, 0.4)) !important;
}

.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__icon::before {
  content: '';
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 2s infinite;
}

.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__label {
  font-weight: 700 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  text-shadow: 0 2rpx 4rpx rgba(102, 126, 234, 0.2) !important;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.container {
  padding: 20rpx;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin: 20rpx 0;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group label {
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.input-group input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  box-sizing: border-box;
}
</style>