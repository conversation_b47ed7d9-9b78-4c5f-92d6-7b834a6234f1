package cn.zhentao.mapper;

import cn.zhentao.pojo.TutorQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI搜题题目Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface TutorQuestionMapper extends BaseMapper<TutorQuestion> {
    
    /**
     * 根据会话ID查询题目列表
     */
    @Select("SELECT * FROM tutor_questions WHERE session_id = #{sessionId} ORDER BY created_time DESC")
    List<TutorQuestion> getQuestionsBySessionId(@Param("sessionId") Long sessionId);
    
    /**
     * 根据用户ID查询错题
     */
    @Select("SELECT * FROM tutor_questions WHERE user_id = #{userId} AND is_wrong = 1 " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{size}")
    List<TutorQuestion> getWrongQuestionsByUserId(@Param("userId") Long userId, 
                                                 @Param("offset") Integer offset, 
                                                 @Param("size") Integer size);
    
    /**
     * 统计用户错题数量
     */
    @Select("SELECT COUNT(*) FROM tutor_questions WHERE user_id = #{userId} AND is_wrong = 1")
    Long countWrongQuestionsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据图片哈希查询题目
     */
    @Select("SELECT * FROM tutor_questions WHERE image_hash = #{imageHash} ORDER BY created_time DESC LIMIT 1")
    TutorQuestion getQuestionByImageHash(@Param("imageHash") String imageHash);
}
