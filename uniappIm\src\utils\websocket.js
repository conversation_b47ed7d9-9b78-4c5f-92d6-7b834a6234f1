let socketTask = null          // WebSocket连接对象
let messageHandler = null      // 消息处理函数
let reconnectTimer = null      // 重连定时器
let heartbeatTimer = null      // 心跳定时器
let reconnectCount = 0         // 重连次数
let maxReconnectCount = 5      // 最大重连次数
let isManualClose = false      // 是否手动关闭

// WebSocket服务器地址
const WS_URL = 'ws://localhost:9999/ws'

/**
 * 连接WebSocket
 * @param {string} token JWT令牌
 * @param {function} onMessage 消息处理回调函数
 */
export function connectWebSocket(token, onMessage) {
  // 如果已有连接，先关闭
  if (socketTask) {
    socketTask.close()
  }
  
  messageHandler = onMessage
  
  // 创建WebSocket连接
  socketTask = uni.connectSocket({
    url: WS_URL,
    success: () => {
      console.log('WebSocket连接成功')
    },
    fail: (err) => {
      console.error('WebSocket连接失败:', err)
    }
  })
  
  // 连接打开事件
  socketTask.onOpen(() => {
    console.log('WebSocket已打开')

    // 重置重连计数
    reconnectCount = 0
    isManualClose = false

    // 发送认证消息
    sendWebSocketMessage({
      type: 'auth',
      token: token
    })

    // 开始心跳
    startHeartbeat()
  })
  
  // 接收消息事件
  socketTask.onMessage((res) => {
    try {
      const message = JSON.parse(res.data)
      console.log('收到WebSocket消息:', message)
      
      if (message.type === 'auth_success') {
        console.log('WebSocket认证成功')
      } else if (message.type === 'heartbeat') {
        console.log('心跳响应')
      } else if (messageHandler) {
        messageHandler(message)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  })
  
  // 连接关闭事件
  socketTask.onClose(() => {
    console.log('WebSocket连接关闭')
    stopHeartbeat()

    // 如果不是手动关闭且重连次数未达到上限，则自动重连
    if (!isManualClose && reconnectCount < maxReconnectCount && !reconnectTimer) {
      reconnectCount++
      const delay = Math.min(1000 * Math.pow(2, reconnectCount), 30000) // 指数退避，最大30秒

      console.log(`第${reconnectCount}次重连WebSocket，${delay}ms后重试`)

      reconnectTimer = setTimeout(() => {
        connectWebSocket(token, messageHandler)
        reconnectTimer = null
      }, delay)
    } else if (reconnectCount >= maxReconnectCount) {
      console.error('WebSocket重连次数已达上限，停止重连')
      uni.showToast({
        title: '网络连接失败，请检查网络',
        icon: 'none'
      })
    }
  })
  
  // 连接错误事件
  socketTask.onError((err) => {
    console.error('WebSocket错误:', err)
  })
}

/**
 * 发送WebSocket消息
 * @param {object} message 要发送的消息对象
 */
export function sendWebSocketMessage(message) {
  if (socketTask && socketTask.readyState === 1) {
    socketTask.send({
      data: JSON.stringify(message),
      success: () => {
        console.log('WebSocket消息发送成功:', message)
      },
      fail: (err) => {
        console.error('WebSocket消息发送失败:', err)
      }
    })
  } else {
    console.error('WebSocket未连接')
  }
}

/**
 * 关闭WebSocket连接
 */
export function closeWebSocket() {
  isManualClose = true  // 标记为手动关闭
  stopHeartbeat()

  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }

  if (socketTask) {
    socketTask.close()
    socketTask = null
  }

  // 重置状态
  reconnectCount = 0
}

/**
 * 开始心跳检测
 */
function startHeartbeat() {
  heartbeatTimer = setInterval(() => {
    sendWebSocketMessage({
      type: 'heartbeat'
    })
  }, 30000) // 30秒心跳
}

/**
 * 停止心跳检测
 */
function stopHeartbeat() {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
    heartbeatTimer = null
  }
}