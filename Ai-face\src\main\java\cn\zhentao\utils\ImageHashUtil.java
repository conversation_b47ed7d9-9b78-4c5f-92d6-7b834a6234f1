package cn.zhentao.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 图片哈希工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ImageHashUtil {

    /**
     * 计算图片的SHA-256哈希值
     * 
     * @param imageBytes 图片字节数组
     * @return 哈希值字符串
     */
    public static String calculateHash(byte[] imageBytes) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(imageBytes);
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (NoSuchAlgorithmException e) {
            log.error("计算图片哈希值失败", e);
            throw new RuntimeException("计算图片哈希值失败", e);
        }
    }

    /**
     * 计算感知哈希值（用于相似图片检测）
     * 
     * @param imageBytes 图片字节数组
     * @return 感知哈希值
     */
    public static String calculatePerceptualHash(byte[] imageBytes) {
        // TODO: 实现感知哈希算法
        // 这里可以使用第三方库如ImageHash来实现
        return calculateHash(imageBytes);
    }
}
