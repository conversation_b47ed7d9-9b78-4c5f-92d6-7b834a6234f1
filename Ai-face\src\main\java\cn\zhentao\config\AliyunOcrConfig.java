package cn.zhentao.config;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OCR配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.ocr")
@Data
public class AliyunOcrConfig {
    
    /**
     * AccessKey ID
     */
    private String accessKeyId;
    
    /**
     * AccessKey Secret
     */
    private String accessKeySecret;
    
    /**
     * 区域ID，默认为cn-shanghai
     */
    private String regionId = "cn-shanghai";
    
    /**
     * 创建阿里云OCR客户端
     * 
     * @return OCR客户端
     * @throws Exception 异常
     */
    @Bean
    public Client createOcrClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        
        // 设置访问的域名
        config.endpoint = "ocr-api." + regionId + ".aliyuncs.com";
        
        return new Client(config);
    }
}
