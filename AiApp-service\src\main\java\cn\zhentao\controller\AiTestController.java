package cn.zhentao.controller;

import cn.zhentao.common.Result;
import cn.zhentao.util.DashScopeAiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * AI测试控制器
 * 用于测试阿里云百炼AI服务的调用
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@RestController
@RequestMapping("/ai")
public class AiTestController {

    @Autowired
    private DashScopeAiUtil dashScopeAiUtil;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "ai-app-service");
        result.put("status", "UP");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "AI App Service is running successfully!");

        return Result.success(result);
    }

    /**
     * 检查AI服务是否可用
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkAiService() {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean isAvailable = dashScopeAiUtil.isServiceAvailable();
            result.put("available", isAvailable);
            result.put("timestamp", LocalDateTime.now());

            if (isAvailable) {
                result.put("message", "AI服务连接正常");
                return Result.success(result);
            } else {
                result.put("message", "AI服务连接失败");
                return Result.error("AI服务不可用");
            }

        } catch (Exception e) {
            log.error("检查AI服务时发生异常: {}", e.getMessage(), e);
            result.put("available", false);
            result.put("error", e.getMessage());
            result.put("timestamp", LocalDateTime.now());
            return Result.error("检查AI服务时发生异常: " + e.getMessage());
        }
    }

    /**
     * 单次AI对话测试
     */
    @PostMapping("/single")
    public Result<Map<String, Object>> singleCall(@RequestBody Map<String, String> request) {
        String prompt = request.get("prompt");

        if (prompt == null || prompt.trim().isEmpty()) {
            return Result.error("prompt参数不能为空");
        }

        try {
            DashScopeAiUtil.AiResponse response = dashScopeAiUtil.singleCall(prompt);

            Map<String, Object> result = new HashMap<>();
            result.put("prompt", prompt);
            result.put("response", response.getText());
            result.put("sessionId", response.getSessionId());
            result.put("success", response.isSuccess());
            result.put("timestamp", LocalDateTime.now());

            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                result.put("error", response.getErrorMessage());
                return Result.error("AI调用失败: " + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("单次AI调用异常: {}", e.getMessage(), e);
            return Result.error("AI调用异常: " + e.getMessage());
        }
    }

    /**
     * 带会话的AI对话测试 - 暂时禁用
     */
    @PostMapping("/session")
    public Result<Map<String, Object>> sessionCall(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "AI会话功能暂时禁用");
        result.put("timestamp", LocalDateTime.now());
        return Result.success(result);
    }

    /**
     * 多轮对话测试 - 暂时禁用
     */
    @PostMapping("/multi-turn")
    public Result<Map<String, Object>> multiTurnConversation() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "多轮对话功能暂时禁用");
        result.put("timestamp", LocalDateTime.now());
        return Result.success(result);
    }

    /**
     * 简单的GET方式AI对话测试
     */
    @GetMapping("/simple")
    public Result<Map<String, Object>> simpleCall(@RequestParam(defaultValue = "你好") String prompt) {
        try {
            DashScopeAiUtil.AiResponse response = dashScopeAiUtil.singleCall(prompt);

            Map<String, Object> result = new HashMap<>();
            result.put("prompt", prompt);
            result.put("response", response.getText());
            result.put("success", response.isSuccess());
            result.put("timestamp", LocalDateTime.now());

            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                result.put("error", response.getErrorMessage());
                return Result.error("AI调用失败: " + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("简单AI调用异常: {}", e.getMessage(), e);
            return Result.error("AI调用异常: " + e.getMessage());
        }
    }

    /**
     * 流式输出AI对话测试
     */
    @GetMapping(value = "/stream", produces = "text/event-stream")
    public SseEmitter streamCall(@RequestParam(defaultValue = "你好") String prompt) {
        log.info("开始流式AI调用，prompt: {}", prompt);
        return dashScopeAiUtil.streamCall(prompt);
    }

    /**
     * 带会话的流式输出AI对话测试
     */
    @PostMapping(value = "/stream", produces = "text/event-stream")
    public SseEmitter streamCallWithSession(@RequestBody Map<String, String> request) {
        String prompt = request.get("prompt");
        String sessionId = request.get("sessionId");

        log.info("开始流式AI会话调用，prompt: {}, sessionId: {}", prompt, sessionId);

        if (prompt == null || prompt.trim().isEmpty()) {
            // 对于流式输出，我们需要返回一个错误的SseEmitter
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("{\"type\":\"error\",\"message\":\"prompt参数不能为空\"}"));
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }

        return dashScopeAiUtil.streamCall(prompt, sessionId);
    }

    /**
     * 快速流式输出AI对话测试（按句子分割）
     */
    @GetMapping(value = "/fast-stream", produces = "text/event-stream")
    public SseEmitter fastStreamCall(@RequestParam(defaultValue = "请介绍一下人工智能的发展历史") String prompt,
                                     @RequestParam(required = false) String sessionId) {
        log.info("开始快速流式AI调用，prompt: {}, sessionId: {}", prompt, sessionId);
        return dashScopeAiUtil.fastStreamCall(prompt, sessionId);
    }
}
