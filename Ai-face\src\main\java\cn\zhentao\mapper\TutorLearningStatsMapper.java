package cn.zhentao.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 学习统计Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface TutorLearningStatsMapper {
    
    /**
     * 获取用户指定天数内的统计汇总
     */
    @Select("SELECT " +
            "SUM(questions_count) as totalQuestions, " +
            "SUM(wrong_count) as wrongQuestions, " +
            "SUM(correct_count) as masteredQuestions, " +
            "COUNT(DISTINCT stat_date) as studyDays, " +
            "SUM(study_time) as studyTimeMinutes " +
            "FROM tutor_learning_stats " +
            "WHERE user_id = #{userId} AND stat_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)")
    Map<String, Object> getUserStatsInDaysSum(@Param("userId") Long userId, @Param("days") Integer days);
}
