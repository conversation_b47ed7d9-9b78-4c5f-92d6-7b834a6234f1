package cn.zhentao.service;

import java.io.InputStream;

/**
 * OCR图片识别服务接口
 * 
 * <AUTHOR>
 */
public interface OcrService {
    
    /**
     * 通用文字识别
     * 
     * @param imageUrl 图片URL
     * @return 识别结果
     */
    String recognizeGeneralText(String imageUrl);
    
    /**
     * 通用文字识别（通过文件流）
     * 
     * @param inputStream 图片文件流
     * @param fileName 文件名
     * @return 识别结果
     */
    String recognizeGeneralText(InputStream inputStream, String fileName);
    
    /**
     * 身份证识别
     * 
     * @param imageUrl 图片URL
     * @param side 正反面（face/back）
     * @return 识别结果
     */
    String recognizeIdCard(String imageUrl, String side);
    
    /**
     * 身份证识别（通过文件流）
     * 
     * @param inputStream 图片文件流
     * @param fileName 文件名
     * @param side 正反面（face/back）
     * @return 识别结果
     */
    String recognizeIdCard(InputStream inputStream, String fileName, String side);
    
    /**
     * 银行卡识别
     * 
     * @param imageUrl 图片URL
     * @return 识别结果
     */
    String recognizeBankCard(String imageUrl);
    
    /**
     * 银行卡识别（通过文件流）
     * 
     * @param inputStream 图片文件流
     * @param fileName 文件名
     * @return 识别结果
     */
    String recognizeBankCard(InputStream inputStream, String fileName);
    
    /**
     * 营业执照识别
     * 
     * @param imageUrl 图片URL
     * @return 识别结果
     */
    String recognizeBusinessLicense(String imageUrl);
    
    /**
     * 营业执照识别（通过文件流）
     * 
     * @param inputStream 图片文件流
     * @param fileName 文件名
     * @return 识别结果
     */
    String recognizeBusinessLicense(InputStream inputStream, String fileName);
}
