import { Button, SafeArea } from 'antd-mobile';

import './index.less';
import { AICallAgentType } from 'aliyun-auikit-aicall';
import { useTranslation } from '@/common/i18nContext';

interface WelcomeProps {
  onAgentTypeSelected: (type: AICallAgentType) => void;
}

function Welcome({ onAgentTypeSelected }: WelcomeProps) {
  const { t } = useTranslation();

  const onClick = () => {
    onAgentTypeSelected(AICallAgentType.VoiceAgent);
  };

  return (
    <div className='welcome'>
      <div className='welcome-header'>{t('welcome.title')}</div>
      <div className='welcome-body'>
        <div className='welcome-swiper'>
          <div className='welcome-img-box'>
            <img
              className='_for-mobile'
              src='https://gw.alicdn.com/imgextra/i2/O1CN01ZLoopi1t0JfazWfy8_!!6000000005839-2-tps-426-852.png'
              alt={t('agent.voice')}
            />
            <img
              className='_for-desktop'
              src='https://gw.alicdn.com/imgextra/i4/O1CN01rXBEYm1oKOyekwSs3_!!6000000005206-2-tps-560-104.png'
              alt={t('agent.voice')}
              width={280}
              height={52}
            />
          </div>
        </div>
        <div className='welcome-btn'>
          <Button color='primary' block onClick={onClick}>
            {t('welcome.btn')}
          </Button>
        </div>
      </div>
      <SafeArea position='bottom' />
    </div>
  );
}

export default Welcome;
