package com.zhentao.studyim.repository;

import com.zhentao.studyim.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问接口
 * JpaRepository提供基本的CRUD操作
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     * Spring Data JPA会自动实现这个方法
     */
    Optional<User> findByUsername(String username);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 根据用户名或手机号搜索用户（模糊匹配）
     * @param username 用户名关键词
     * @param phonenumber 手机号关键词
     * @return 用户列表
     */
    List<User> findByUsernameContainingOrPhonenumberContaining(String username, String phonenumber);

    /**
     * 根据用户名精确查找用户
     * @param username 用户名
     * @return 用户
     */
    Optional<User> findByUsernameIgnoreCase(String username);

    /**
     * 根据手机号精确查找用户
     * @param phonenumber 手机号
     * @return 用户
     */
    Optional<User> findByPhonenumber(String phonenumber);
}