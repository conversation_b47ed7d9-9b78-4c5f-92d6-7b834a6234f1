package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目缓存实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_cache")
public class QuestionCache {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 图片哈希值
     */
    private String imageHash;

    /**
     * OCR识别结果
     */
    private String ocrText;

    /**
     * LaTeX公式
     */
    private String ocrLatex;

    /**
     * AI分析结果
     */
    private String aiAnalysis;

    /**
     * 解题步骤
     */
    private List<String> solutionSteps;

    /**
     * 最终答案
     */
    private String finalAnswer;

    /**
     * 学科
     */
    private String subject;

    /**
     * 题型
     */
    private String questionType;

    /**
     * 命中次数
     */
    private Integer hitCount;

    /**
     * 最后命中时间
     */
    private LocalDateTime lastHitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
