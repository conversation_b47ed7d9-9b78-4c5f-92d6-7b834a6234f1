// 应用配置
export const config = {
  // API基础地址
  baseURL: 'http://localhost:8080/api',
  
  // WebSocket地址
  websocketURL: 'ws://localhost:9998/websocket',
  
  // 应用信息
  appName: '即时通讯',
  version: '1.0.0',
  
  // 分页配置
  pageSize: 20,
  
  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowTypes: ['image/jpeg', 'image/png', 'image/gif']
  },
  
  // 消息配置
  message: {
    maxLength: 1000, // 消息最大长度
    retryTimes: 3,   // 重试次数
    timeout: 5000    // 超时时间(ms)
  }
}

// 环境配置
export const env = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:8080/api',
    websocketURL: 'ws://localhost:9998/websocket',
    debug: true
  },
  
  // 生产环境
  production: {
    baseURL: 'https://your-api-domain.com/api',
    websocketURL: 'wss://your-websocket-domain.com/websocket',
    debug: false
  }
}

// 获取当前环境配置
export function getCurrentConfig() {
  // #ifdef H5
  const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
  return isDev ? env.development : env.production
  // #endif
  
  // #ifdef MP-WEIXIN
  return env.development // 小程序默认使用开发环境
  // #endif
  
  // #ifdef APP-PLUS
  return env.production // App默认使用生产环境
  // #endif
  
  return env.development
}
