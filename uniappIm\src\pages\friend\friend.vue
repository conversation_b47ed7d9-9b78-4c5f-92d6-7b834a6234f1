<template>
  <view class="friend-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <text class="title">好友管理</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <text v-if="tab.badge > 0 && tab.showBadge" class="tab-badge">{{ tab.badge }}</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y="true">
      <!-- 好友列表 -->
      <view v-if="currentTab === 0" class="friend-list">
        <!-- 搜索框 -->
        <view class="search-section">
          <view class="search-box">
            <view class="search-icon">🔍</view>
            <input
              v-model="friendSearchKeyword"
              class="search-input"
              placeholder="搜索好友（昵称/备注/用户名）"
              @input="onFriendSearch"
            />
            <view v-if="friendSearchKeyword" class="clear-icon" @click="clearFriendSearch">
              <text>×</text>
            </view>
          </view>
        </view>
        <view v-for="friend in filteredFriendList" :key="friend.userId" class="friend-item" @click="openChat(friend)">
          <view class="avatar">
            <text class="avatar-text">{{ getDisplayName(friend).charAt(0) }}</text>
          </view>
          <view class="friend-info">
            <text class="friend-name">{{ getDisplayName(friend) }}</text>
            <text class="friend-username">@{{ friend.username }}</text>
            <text v-if="friend.remark" class="friend-remark">备注: {{ friend.remark }}</text>
          </view>
          <view class="friend-actions">
            <view class="action-btn remark-btn" @click.stop="showRemarkModal(friend)">
              <view class="btn-icon">
                <text class="icon-text">备注</text>
              </view>
            </view>
            <view class="action-btn delete-btn" @click.stop="deleteFriendConfirm(friend)">
              <view class="btn-icon">
                <text class="icon-text">删除</text>
              </view>
            </view>
          </view>
        </view>
        
        <view v-if="filteredFriendList.length === 0" class="empty-state">
          <text class="empty-text">{{ friendSearchKeyword ? '未找到相关好友' : '暂无好友' }}</text>
          <text class="empty-hint">{{ friendSearchKeyword ? '尝试其他关键词' : '点击右上角搜索添加好友' }}</text>
        </view>
      </view>

      <!-- 收到的申请 -->
      <view v-if="currentTab === 1" class="request-list">
        <view v-for="request in receivedRequests" :key="request.id" class="request-item">
          <view class="avatar">
            <text class="avatar-text">{{ (request.fromUser.nickname || request.fromUser.username || '?').charAt(0) }}</text>
          </view>
          <view class="request-info">
            <text class="request-name">{{ request.fromUser.nickname || request.fromUser.username }}</text>
            <text class="request-message">{{ request.message || '请求添加您为好友' }}</text>
            <text class="request-time">{{ formatTime(request.createTime) }}</text>
          </view>
          <view class="request-actions">
            <view class="accept-btn" @click="handleRequest(request.id, 'accept')">
              <text>同意</text>
            </view>
            <view class="reject-btn" @click="handleRequest(request.id, 'reject')">
              <text>拒绝</text>
            </view>
          </view>
        </view>
        
        <view v-if="receivedRequests.length === 0" class="empty-state">
          <text class="empty-text">暂无好友申请</text>
        </view>
      </view>

      <!-- 发出的申请 -->
      <view v-if="currentTab === 2" class="request-list">
        <view v-for="request in sentRequests" :key="request.id" class="request-item">
          <view class="avatar">
            <text class="avatar-text">{{ (request.toUser.nickname || request.toUser.username || '?').charAt(0) }}</text>
          </view>
          <view class="request-info">
            <text class="request-name">{{ request.toUser.nickname || request.toUser.username }}</text>
            <text class="request-message">{{ request.message || '请求添加为好友' }}</text>
            <text class="request-time">{{ formatTime(request.createTime) }}</text>
          </view>
          <view class="request-status">
            <text class="status-text" :class="getStatusClass(request.status)">
              {{ getStatusText(request.status) }}
            </text>
          </view>
        </view>
        
        <view v-if="sentRequests.length === 0" class="empty-state">
          <text class="empty-text">暂无发出的申请</text>
        </view>
      </view>
    </scroll-view>

    <!-- 搜索弹窗 -->
    <view v-if="showSearchModalFlag" class="search-modal" @click="hideSearchModal">
      <view class="search-content" @click.stop>
        <view class="search-header">
          <text class="search-title">搜索用户</text>
          <view class="close-btn" @click="hideSearchModal">
            <text>✕</text>
          </view>
        </view>
        
        <view class="search-input-group">
          <input 
            v-model="searchKeyword" 
            placeholder="输入用户名或昵称" 
            class="search-input"
            @input="onSearchInput"
          />
          <view class="search-btn" @click="searchUsers">
            <text>搜索</text>
          </view>
        </view>
        
        <scroll-view class="search-results" scroll-y="true">
          <view v-for="user in searchResults" :key="user.id" class="search-item">
            <view class="avatar">
              <text class="avatar-text">{{ (user.nickname || user.username || '?').charAt(0) }}</text>
            </view>
            <view class="user-info">
              <text class="user-name">{{ user.nickname || user.username }}</text>
              <text class="user-username">@{{ user.username }}</text>
            </view>
            <view class="add-btn" @click="sendFriendRequestToUser(user)">
              <text>添加</text>
            </view>
          </view>
          
          <view v-if="searchResults.length === 0 && searchKeyword" class="empty-state">
            <text class="empty-text">未找到相关用户</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 备注设置模态框 -->
    <view v-if="showRemarkModalFlag" class="modal-overlay" @click="hideRemarkModal">
      <view class="remark-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">设置备注</text>
          <view class="close-btn" @click="hideRemarkModal">
            <text class="close-icon">×</text>
          </view>
        </view>
        <view class="modal-content">
          <view class="friend-preview">
            <view class="preview-avatar">
              <text class="preview-avatar-text">{{ getDisplayName(selectedFriend).charAt(0) }}</text>
            </view>
            <view class="preview-info">
              <text class="preview-name">{{ getDisplayName(selectedFriend) }}</text>
              <text class="preview-username">@{{ selectedFriend.username }}</text>
            </view>
          </view>
          <view class="input-section">
            <text class="input-label">备注名称</text>
            <textarea
              v-model="remarkInput"
              class="remark-input"
              placeholder="请输入备注名称"
              maxlength="20"
              auto-height
              :show-confirm-bar="false"
              @input="onRemarkInput"
              @focus="onInputFocus"
              @blur="onInputBlur"
            />
          </view>
        </view>
        <view class="modal-footer">
          <button class="cancel-btn" @click="hideRemarkModal">取消</button>
          <button class="confirm-btn" @click="saveRemark" :disabled="saving">
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 自定义底部导航栏 -->
    <TabBar :current="1" @change="onTabChange" />
  </view>
</template>

<script>
import {
  getFriendList,
  getReceivedFriendRequests,
  getSentFriendRequests,
  handleFriendRequest,
  deleteFriend,
  searchUsers,
  sendFriendRequest,
  setFriendRemark
} from '@/utils/api.js'
import TabBar from '@/components/TabBar/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: '好友列表', badge: 0, showBadge: false },
        { name: '收到申请', badge: 0, showBadge: true },
        { name: '发出申请', badge: 0, showBadge: false }
      ],
      friendList: [],
      receivedRequests: [],
      sentRequests: [],
      showSearchModalFlag: false,
      searchKeyword: '',
      searchResults: [],
      showRemarkModalFlag: false,
      selectedFriend: {},
      remarkInput: '',
      saving: false,
      friendSearchKeyword: ''
    }
  },

  onLoad() {
    this.loadAllData()
  },

  computed: {
    filteredFriendList() {
      if (!this.friendSearchKeyword.trim()) {
        return this.friendList
      }

      const keyword = this.friendSearchKeyword.toLowerCase()
      return this.friendList.filter(friend => {
        // 搜索备注、昵称、用户名
        const remark = (friend.remark || '').toLowerCase()
        const nickname = (friend.nickname || '').toLowerCase()
        const username = (friend.username || '').toLowerCase()

        return remark.includes(keyword) ||
               nickname.includes(keyword) ||
               username.includes(keyword)
      })
    }
  },

  onShow() {
    this.loadAllData()
  },

  methods: {
    switchTab(index) {
      this.currentTab = index
    },

    async loadAllData() {
      await Promise.all([
        this.loadFriendList(),
        this.loadReceivedRequests(),
        this.loadSentRequests()
      ])
    },

    async loadFriendList() {
      try {
        const result = await getFriendList()
        console.log('好友列表API响应:', result)
        if (result.code === 200) {
          this.friendList = result.data || []
          console.log('好友列表数据:', this.friendList)
          // 好友列表不显示数量徽章
          // this.tabs[0].badge = this.friendList.length
        }
      } catch (error) {
        console.error('加载好友列表失败:', error)
      }
    },

    async loadReceivedRequests() {
      try {
        const result = await getReceivedFriendRequests()
        if (result.code === 200) {
          this.receivedRequests = result.data || []
          this.tabs[1].badge = this.receivedRequests.filter(r => r.status === 'PENDING').length
        }
      } catch (error) {
        console.error('加载收到的申请失败:', error)
      }
    },

    async loadSentRequests() {
      try {
        const result = await getSentFriendRequests()
        if (result.code === 200) {
          this.sentRequests = result.data || []
          this.tabs[2].badge = this.sentRequests.length
        }
      } catch (error) {
        console.error('加载发出的申请失败:', error)
      }
    },

    async handleRequest(requestId, action) {
      try {
        const result = await handleFriendRequest(requestId, action)
        if (result.code === 200) {
          uni.showToast({
            title: action === 'accept' ? '已同意' : '已拒绝',
            icon: 'success'
          })
          // 重新加载数据，这会自动更新红色提醒数量
          await this.loadAllData()

          // 如果处理完所有申请，清除红色提醒
          if (this.receivedRequests.length === 0) {
            this.tabs[1].badge = 0
          }
        } else {
          uni.showToast({
            title: result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    deleteFriendConfirm(friend) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除好友 ${friend.nickname || friend.username} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.deleteFriendAction(friend.userId || friend.id)
          }
        }
      })
    },

    async deleteFriendAction(friendId) {
      try {
        const result = await deleteFriend(friendId)
        if (result.code === 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          this.loadFriendList()
        } else {
          uni.showToast({
            title: result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    },

    openChat(friend) {
      const userId = friend.userId || friend.id
      const displayName = this.getDisplayName(friend)
      uni.navigateTo({
        url: `/pages/chatDetail/chatDetail?userId=${userId}&nickname=${encodeURIComponent(displayName)}`
      })
    },

    getDisplayName(friend) {
      if (friend.remark && friend.remark.trim()) {
        return friend.remark
      }
      return friend.nickname || friend.username || '未知用户'
    },

    showRemarkModal(friend) {
      console.log('显示备注模态框，好友数据:', friend)
      this.selectedFriend = friend
      this.remarkInput = friend.remark || ''
      this.showRemarkModalFlag = true
    },

    hideRemarkModal() {
      this.showRemarkModalFlag = false
      this.selectedFriend = {}
      this.remarkInput = ''
      this.saving = false
    },

    onRemarkInput(e) {
      this.remarkInput = e.detail.value
    },

    onInputFocus() {
      console.log('输入框获得焦点')
    },

    onInputBlur() {
      console.log('输入框失去焦点')
    },

    async saveRemark() {
      if (this.saving) return

      this.saving = true
      try {
        const result = await setFriendRemark(this.selectedFriend.userId, this.remarkInput.trim())
        if (result.code === 200) {
          uni.showToast({
            title: '备注设置成功',
            icon: 'success'
          })
          this.hideRemarkModal()
          this.loadFriendList() // 重新加载好友列表
        } else {
          uni.showToast({
            title: result.message || '设置失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('设置备注失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },

    onFriendSearch() {
      // 搜索是通过计算属性实时进行的，这里可以添加防抖逻辑
      // 暂时不需要额外处理
    },

    clearFriendSearch() {
      this.friendSearchKeyword = ''
    },

    showSearchModal() {
      this.showSearchModalFlag = true
    },

    hideSearchModal() {
      this.showSearchModalFlag = false
      this.searchKeyword = ''
      this.searchResults = []
    },

    onSearchInput() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
      }
    },

    async searchUsers() {
      if (!this.searchKeyword.trim()) {
        uni.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        })
        return
      }

      try {
        const result = await searchUsers(this.searchKeyword)
        if (result.code === 200) {
          this.searchResults = result.data || []
        } else {
          uni.showToast({
            title: result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        })
      }
    },

    async sendFriendRequestToUser(user) {
      try {
        const result = await sendFriendRequest({
          toUserId: user.userId || user.id,
          message: '请求添加您为好友'
        })
        if (result.code === 200) {
          uni.showToast({
            title: '申请已发送',
            icon: 'success'
          })
          this.hideSearchModal()
        } else {
          uni.showToast({
            title: result.message,
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    },

    formatTime(timeStr) {
      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      return Math.floor(diff / 86400000) + '天前'
    },

    /**
     * 处理底部导航栏切换
     */
    onTabChange(index) {
      console.log('切换到标签:', index)
    },

    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已同意',
        'REJECTED': '已拒绝'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      return {
        'status-pending': status === 'PENDING',
        'status-accepted': status === 'ACCEPTED',
        'status-rejected': status === 'REJECTED'
      }
    }
  }
}
</script>

<style scoped>
.friend-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  height: 88rpx;
}

.title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2rpx 20rpx rgba(102, 126, 234, 0.08);
  position: relative;
}

.tabs::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

.tab-item {
  flex: 1;
  padding: 32rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  gap: 12rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 16rpx 16rpx 0 0;
  margin: 0 4rpx;
}

.tab-item:active {
  transform: scale(0.96);
}

.tab-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  transform: translateY(-2rpx);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx 3rpx 0 0;
  box-shadow: 0 -2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.tab-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #666;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tab-item.active .tab-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  transform: scale(1.05);
}

.tab-badge {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  font-size: 20rpx;
  font-weight: 700;
  padding: 6rpx 10rpx;
  border-radius: 20rpx;
  min-width: 28rpx;
  text-align: center;
  line-height: 1;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  }
}

/* 内容区域 */
.content {
  flex: 1;
  background: #f5f5f5;
  padding-bottom: 100rpx; /* 为底部导航栏留出空间 */
}

/* 好友列表样式 */
.friend-list, .request-list {
  padding: 20rpx;
}

.friend-item, .request-item, .search-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.friend-info, .request-info, .user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.friend-name, .request-name, .user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.friend-username, .user-username {
  font-size: 24rpx;
  color: #999;
}

.request-message {
  font-size: 26rpx;
  color: #666;
}

.request-time {
  font-size: 22rpx;
  color: #999;
}

/* 操作按钮样式 */
.friend-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  height: 56rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.remark-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 24rpx;
  min-width: 88rpx;
}

.remark-btn:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(0.95);
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  padding: 0 24rpx;
  min-width: 88rpx;
}

.delete-btn:active {
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
  transform: scale(0.95);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.request-actions {
  display: flex;
  gap: 20rpx;
}

.accept-btn, .reject-btn, .add-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  text-align: center;
}

.accept-btn, .add-btn {
  background: #667eea;
  color: white;
}

.reject-btn {
  background: #ff4757;
  color: white;
}

.request-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-pending {
  background: #ffa502;
  color: white;
}

.status-accepted {
  background: #2ed573;
  color: white;
}

.status-rejected {
  background: #ff4757;
  color: white;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.empty-hint {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 搜索弹窗样式 */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.search-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.search-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}



.search-input-group {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.search-btn {
  padding: 20rpx 40rpx;
  background: #667eea;
  color: white;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
}

.search-results {
  flex: 1;
  padding: 20rpx;
  max-height: 600rpx;
}

/* 备注设置模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.remark-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(245, 245, 245, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: rgba(230, 230, 230, 0.8);
  transform: scale(0.9);
}

.close-icon {
  font-size: 32rpx;
  color: #999;
}

.modal-content {
  padding: 40rpx 30rpx;
}

.friend-preview {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(248, 249, 250, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.preview-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.preview-avatar-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.preview-info {
  flex: 1;
}

.preview-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.preview-username {
  font-size: 24rpx;
  color: #888;
  display: block;
}

.input-section {
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.remark-input {
  width: 100%;
  min-height: 80rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(229, 229, 229, 0.6);
  border-radius: 16rpx;
  font-size: 32rpx;
  background: rgba(255, 255, 255, 1);
  transition: all 0.3s ease;
  box-sizing: border-box;
  color: #333;
  line-height: 1.4;
  resize: none;
}

.remark-input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.cancel-btn {
  background: rgba(245, 245, 245, 0.8);
  backdrop-filter: blur(10rpx);
  color: #666;
  border: 1rpx solid rgba(229, 229, 229, 0.6);
}

.cancel-btn:active {
  background: rgba(232, 232, 232, 0.8);
  transform: scale(0.98);
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.confirm-btn:disabled {
  background: rgba(204, 204, 204, 0.8);
  color: #999;
  box-shadow: none;
}

.friend-remark {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
  display: block;
}

/* 好友搜索样式 */
.search-section {
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(102, 126, 234, 0.15);
  border: 2rpx solid #667eea;
  padding: 14rpx 18rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}

.clear-icon:active {
  background: #ccc;
  transform: scale(0.9);
}
</style>