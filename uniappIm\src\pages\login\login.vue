<template>
  <view class="login-container">
    <!-- 登录头部 -->
    <view class="login-header">
      <view class="logo-section">
        <view class="logo-container">
          <view class="logo-icon">🤖</view>
          <view class="logo-glow"></view>
        </view>
        <text class="brand-name">伴伴AI</text>
        <text class="brand-slogan">智能陪伴，温暖相随</text>
      </view>
      <view class="floating-elements">
        <view class="float-item float-1">✨</view>
        <view class="float-item float-2">💫</view>
        <view class="float-item float-3">🌟</view>
      </view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-title">
        <text class="welcome-text">欢迎回来</text>
        <text class="welcome-subtitle">登录您的伴伴AI账户</text>
      </view>

      <view class="input-group">
        <view class="input-icon">👤</view>
        <input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          class="input-field"
        />
      </view>

      <view class="input-group">
        <view class="input-icon">🔒</view>
        <input
          v-model="loginForm.password"
          placeholder="请输入密码"
          type="password"
          class="input-field"
        />
      </view>

      <button @click="handleLogin" class="login-btn" :disabled="loading">
        <view class="btn-content">
          <text v-if="loading" class="loading-icon">⏳</text>
          <text class="btn-text">{{ loading ? "登录中..." : "开始AI之旅" }}</text>
        </view>
      </button>

      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">或</text>
        <view class="divider-line"></view>
      </view>

      <view class="register-link">
        <text @click="handleRegister">还没有账号？立即注册</text>
      </view>


    </view>

    <!-- 注册弹窗 -->
    <view
      v-if="showRegisterModal"
      class="modal-overlay"
      @click="closeRegisterPopup"
    >
      <view class="register-popup" @click.stop>
        <view class="popup-header">
          <view class="popup-icon">🎉</view>
          <view class="popup-title">加入伴伴AI</view>
          <view class="popup-subtitle">开启您的AI陪伴之旅</view>
        </view>

        <view class="input-group">
          <view class="input-icon">👤</view>
          <input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            class="input-field"
          />
        </view>
        <view class="input-group">
          <view class="input-icon">🔒</view>
          <input
            v-model="registerForm.password"
            placeholder="请输入密码"
            type="password"
            class="input-field"
          />
        </view>
        <view class="input-group">
          <view class="input-icon">✨</view>
          <input
            v-model="registerForm.nickname"
            placeholder="请输入昵称（可选）"
            class="input-field"
          />
        </view>
        <view class="popup-buttons">
          <button @click="closeRegisterPopup" class="cancel-btn">
            <text>取消</text>
          </button>
          <button @click="submitRegister" class="confirm-btn">
            <text class="confirm-icon">🚀</text>
            <text>立即注册</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { login, register, getUserList } from "@/utils/api.js";

export default {
  data() {
    return {
      loading: false, // 登录加载状态
      showRegisterModal: false, // 注册弹窗显示状态
      loginForm: {
        // 登录表单数据
        username: "", // 用户输入账号
        password: "", // 用户输入密码
      },
      registerForm: {
        // 注册表单数据
        username: "",
        password: "",
        nickname: "",
      },
    };
  },
  methods: {
    /**
     * 处理登录
     */
    async handleLogin() {
      // 表单验证
      if (!this.loginForm.username || !this.loginForm.password) {
        uni.showToast({
          title: "请填写完整信息",
          icon: "none",
        });
        return;
      }

      this.loading = true;
      try {
        console.log("=== 开始登录 ===");
        console.log("登录表单数据:", this.loginForm);
        console.log("API地址:", "http://localhost:8080/api/auth/login");

        // 调用登录API
        const result = await login(this.loginForm);
        console.log("登录API响应:", result);

        if (result.code === 200) {
          // 保存token和用户信息到本地存储
          uni.setStorageSync("token", result.data.token);
          uni.setStorageSync("userInfo", result.data.user);

          console.log("登录成功，保存的用户信息:", result.data.user);

          uni.showToast({
            title: "登录成功",
            icon: "success",
          });

          // 跳转到聊天页面
          uni.reLaunch({
            url: "/pages/chat/chat",
          });
        } else {
          console.log("登录失败:", result.message);
          uni.showToast({
            title: result.message || "登录失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("登录异常:", error);
        uni.showToast({
          title: "网络错误，请检查后端服务: " + error.message,
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 显示注册弹窗
     */
    handleRegister() {
      this.showRegisterModal = true;
    },

    /**
     * 关闭注册弹窗
     */
    closeRegisterPopup() {
      this.showRegisterModal = false;
      this.registerForm = {
        username: "",
        password: "",
        nickname: "",
      };
    },

    /**
     * 提交注册
     */
    async submitRegister() {
      // 表单验证
      if (!this.registerForm.username || !this.registerForm.password) {
        uni.showToast({
          title: "请填写完整信息",
          icon: "none",
        });
        return;
      }

      try {
        // 调用注册API
        const result = await register(this.registerForm);
        if (result.code === 200) {
          uni.showToast({
            title: "注册成功",
            icon: "success",
          });
          this.closeRegisterPopup();

          // 自动填充登录表单
          this.loginForm.username = this.registerForm.username;
          this.loginForm.password = this.registerForm.password;
        } else {
          uni.showToast({
            title: result.message,
            icon: "none",
          });
        }
      } catch (error) {
        uni.showToast({
          title: "注册失败",
          icon: "none",
        });
      }
    },


  },
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 2;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.logo-container {
  position: relative;
  margin-bottom: 30rpx;
}

.logo-icon {
  font-size: 120rpx;
  position: relative;
  z-index: 2;
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160rpx;
  height: 160rpx;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow 2s ease-in-out infinite alternate;
}

.brand-name {
  font-size: 64rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.3);
  letter-spacing: 4rpx;
}

.brand-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 2rpx;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.float-item {
  position: absolute;
  font-size: 32rpx;
  color: rgba(255,255,255,0.6);
  animation: float 4s ease-in-out infinite;
}

.float-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.float-2 {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.float-3 {
  top: 60%;
  left: 10%;
  animation-delay: 2s;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes glow {
  0% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-20rpx) rotate(180deg); opacity: 1; }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.form-title {
  text-align: center;
  margin-bottom: 50rpx;
}

.welcome-text {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
}

.welcome-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 300;
}

.input-group {
  margin-bottom: 32rpx;
  position: relative;
}

.input-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  z-index: 2;
}

.input-field {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 24rpx 0 72rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background: rgba(248, 249, 250, 0.8);
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: #e8e8e8;
}

.divider-text {
  padding: 0 24rpx;
  color: #999;
  font-size: 24rpx;
}

.register-link {
  text-align: center;
}

.register-link text {
  color: #667eea;
  font-size: 28rpx;
  font-weight: 500;
  text-decoration: underline;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.register-popup {
  width: 640rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  margin: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: slideUp 0.3s ease;
}

.popup-header {
  text-align: center;
  margin-bottom: 50rpx;
}

.popup-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.popup-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.popup-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.popup-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 50rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.cancel-btn:active {
  background: #e9ecef;
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn:active {
  transform: translateY(2rpx);
}

.confirm-icon {
  font-size: 24rpx;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(60rpx); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}


</style>
