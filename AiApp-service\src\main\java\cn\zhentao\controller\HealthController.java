package cn.zhentao.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单的健康检查控制器
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@RestController
@RequestMapping("/health")
public class HealthController {

    @GetMapping
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "ai-app-service");
        result.put("timestamp", LocalDateTime.now().toString());
        result.put("message", "AI App Service is running!");
        return result;
    }

    @GetMapping("/simple")
    public String simpleHealth() {
        return "AI App Service is UP and running at " + LocalDateTime.now();
    }
}
